{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/EmailNotificationSettings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Form, Switch, Button, Input, Select, message, Divider, Space, Typography } from 'antd';\nimport { MailOutlined, SaveOutlined, SendOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  Option\n} = Select;\nconst {\n  TextArea\n} = Input;\nconst EmailNotificationSettings = () => {\n  _s();\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [testEmailLoading, setTestEmailLoading] = useState(false);\n  const [settings, setSettings] = useState({\n    enabled: false,\n    sendOnNewScholarship: true,\n    sendOnScholarshipUpdate: false,\n    emailTemplate: 'New scholarship available: {{scholarshipTitle}} - Check it out now!',\n    frequency: 'immediate',\n    lastSent: null\n  });\n\n  // No API needed for mock implementation\n\n  useEffect(() => {\n    fetchSettings();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  const fetchSettings = async () => {\n    try {\n      setLoading(true);\n\n      // Use mock data instead of API call\n      console.log('Fetching notification settings (mock)');\n\n      // Get settings from localStorage or use defaults\n      const savedSettings = localStorage.getItem('email_notification_settings');\n\n      // Default settings\n      const data = {\n        enabled: false,\n        sendOnNewScholarship: true,\n        sendOnScholarshipUpdate: false,\n        emailTemplate: 'New scholarship available: {{scholarshipTitle}} - Check it out now!',\n        frequency: 'immediate',\n        lastSent: null\n      };\n\n      // If we have saved settings, use them\n      if (savedSettings) {\n        try {\n          const parsedSettings = JSON.parse(savedSettings);\n          if (typeof parsedSettings.enabled === 'boolean') data.enabled = parsedSettings.enabled;\n          if (typeof parsedSettings.sendOnNewScholarship === 'boolean') data.sendOnNewScholarship = parsedSettings.sendOnNewScholarship;\n          if (typeof parsedSettings.sendOnScholarshipUpdate === 'boolean') data.sendOnScholarshipUpdate = parsedSettings.sendOnScholarshipUpdate;\n          if (typeof parsedSettings.emailTemplate === 'string') data.emailTemplate = parsedSettings.emailTemplate;\n          if (typeof parsedSettings.frequency === 'string') data.frequency = parsedSettings.frequency;\n          if (parsedSettings.lastSent) data.lastSent = parsedSettings.lastSent;\n        } catch (e) {\n          console.error('Error parsing saved settings:', e);\n        }\n      }\n      setSettings(data);\n      form.setFieldsValue(data);\n      console.log('Notification settings loaded:', data);\n    } catch (error) {\n      console.error('Error fetching notification settings:', error);\n      message.error('Failed to load notification settings');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSave = async values => {\n    try {\n      setLoading(true);\n\n      // Save settings to localStorage\n      console.log('Saving notification settings:', values);\n      localStorage.setItem('email_notification_settings', JSON.stringify(values));\n\n      // Update state\n      setSettings(values);\n\n      // Simulate API delay\n      await new Promise(resolve => setTimeout(resolve, 500));\n      message.success('Notification settings saved successfully');\n    } catch (error) {\n      console.error('Error saving notification settings:', error);\n      message.error('Failed to save notification settings');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const sendTestEmail = async () => {\n    try {\n      setTestEmailLoading(true);\n\n      // Get current form values\n      const formValues = form.getFieldsValue();\n      console.log('Sending test email with settings:', formValues);\n\n      // Simulate API delay\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      // Show success message with template preview\n      const template = formValues.emailTemplate || 'New scholarship available: {{scholarshipTitle}} - Check it out now!';\n      const previewTemplate = template.replace('{{scholarshipTitle}}', 'Example Scholarship').replace('{{scholarshipLink}}', 'https://example.com/scholarship/123').replace('{{scholarshipDeadline}}', '2024-12-31');\n      message.success(/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Test email sent <NAME_EMAIL>\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '8px',\n            fontStyle: 'italic'\n          },\n          children: [\"Preview: \", previewTemplate]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this));\n    } catch (error) {\n      console.error('Error sending test email:', error);\n      message.error('Failed to send test email');\n    } finally {\n      setTestEmailLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    title: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center\",\n      children: [/*#__PURE__*/_jsxDEV(MailOutlined, {\n        className: \"mr-2 text-blue-500\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: \"Email Notification Settings\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 144,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this),\n    className: \"shadow-md\",\n    children: /*#__PURE__*/_jsxDEV(Form, {\n      form: form,\n      layout: \"vertical\",\n      initialValues: settings,\n      onFinish: handleSave,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 5,\n          children: \"General Settings\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: \"Configure when and how email notifications are sent to subscribers\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        name: \"enabled\",\n        valuePropName: \"checked\",\n        label: \"Enable Email Notifications\",\n        children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 166,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 5,\n          children: \"Notification Triggers\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: \"Select which events should trigger email notifications\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        name: \"sendOnNewScholarship\",\n        valuePropName: \"checked\",\n        label: \"New Scholarships\",\n        children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        name: \"sendOnScholarshipUpdate\",\n        valuePropName: \"checked\",\n        label: \"Scholarship Updates\",\n        children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        name: \"frequency\",\n        label: \"Notification Frequency\",\n        children: /*#__PURE__*/_jsxDEV(Select, {\n          children: [/*#__PURE__*/_jsxDEV(Option, {\n            value: \"immediate\",\n            children: \"Immediate\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Option, {\n            value: \"daily\",\n            children: \"Daily Digest\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Option, {\n            value: \"weekly\",\n            children: \"Weekly Digest\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(Title, {\n          level: 5,\n          children: \"Email Template\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Text, {\n          type: \"secondary\",\n          children: [\"Customize the email template. You can use the following variables:\", /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"list-disc ml-5 mt-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"li\", {\n              children: ['{{scholarshipTitle}}', \" - The title of the scholarship\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: ['{{scholarshipLink}}', \" - The link to the scholarship\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n              children: ['{{scholarshipDeadline}}', \" - The deadline of the scholarship\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        name: \"emailTemplate\",\n        label: \"Email Template\",\n        children: /*#__PURE__*/_jsxDEV(TextArea, {\n          rows: 4\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 218,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n        children: /*#__PURE__*/_jsxDEV(Space, {\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            htmlType: \"submit\",\n            loading: loading,\n            icon: /*#__PURE__*/_jsxDEV(SaveOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 21\n            }, this),\n            children: \"Save Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            onClick: sendTestEmail,\n            loading: testEmailLoading,\n            icon: /*#__PURE__*/_jsxDEV(SendOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 234,\n              columnNumber: 21\n            }, this),\n            children: \"Send Test Email\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 221,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 141,\n    columnNumber: 5\n  }, this);\n};\n_s(EmailNotificationSettings, \"/A2cGkWxBVgFlG4nTzdEZTFAmnA=\", false, function () {\n  return [Form.useForm];\n});\n_c = EmailNotificationSettings;\nexport default EmailNotificationSettings;\nvar _c;\n$RefreshReg$(_c, \"EmailNotificationSettings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Form", "Switch", "<PERSON><PERSON>", "Input", "Select", "message", "Divider", "Space", "Typography", "MailOutlined", "SaveOutlined", "SendOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "Option", "TextArea", "EmailNotificationSettings", "_s", "form", "useForm", "loading", "setLoading", "testEmailLoading", "setTestEmailLoading", "settings", "setSettings", "enabled", "sendOnNewScholarship", "sendOnScholarshipUpdate", "emailTemplate", "frequency", "lastSent", "fetchSettings", "console", "log", "savedSettings", "localStorage", "getItem", "data", "parsedSettings", "JSON", "parse", "e", "error", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleSave", "values", "setItem", "stringify", "Promise", "resolve", "setTimeout", "success", "sendTestEmail", "formValues", "getFieldsValue", "template", "previewTemplate", "replace", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginTop", "fontStyle", "title", "className", "layout", "initialValues", "onFinish", "level", "type", "<PERSON><PERSON>", "name", "valuePropName", "label", "value", "rows", "htmlType", "icon", "onClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/EmailNotificationSettings.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Card, Form, Switch, Button, Input, Select, message, Divider, Space, Typography } from 'antd';\n\nimport { MailOutlined, SaveOutlined, SendOutlined } from '@ant-design/icons';\n\nconst { Title, Text } = Typography;\nconst { Option } = Select;\nconst { TextArea } = Input;\n\ninterface NotificationSettings {\n  enabled: boolean;\n  sendOnNewScholarship: boolean;\n  sendOnScholarshipUpdate: boolean;\n  emailTemplate: string;\n  frequency: 'immediate' | 'daily' | 'weekly';\n  lastSent: string | null;\n}\n\nconst EmailNotificationSettings: React.FC = () => {\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [testEmailLoading, setTestEmailLoading] = useState(false);\n  const [settings, setSettings] = useState<NotificationSettings>({\n    enabled: false,\n    sendOnNewScholarship: true,\n    sendOnScholarshipUpdate: false,\n    emailTemplate: 'New scholarship available: {{scholarshipTitle}} - Check it out now!',\n    frequency: 'immediate',\n    lastSent: null\n  });\n\n  // No API needed for mock implementation\n\n  useEffect(() => {\n    fetchSettings();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  const fetchSettings = async () => {\n    try {\n      setLoading(true);\n\n      // Use mock data instead of API call\n      console.log('Fetching notification settings (mock)');\n\n      // Get settings from localStorage or use defaults\n      const savedSettings = localStorage.getItem('email_notification_settings');\n\n      // Default settings\n      const data: NotificationSettings = {\n        enabled: false,\n        sendOnNewScholarship: true,\n        sendOnScholarshipUpdate: false,\n        emailTemplate: 'New scholarship available: {{scholarshipTitle}} - Check it out now!',\n        frequency: 'immediate',\n        lastSent: null\n      };\n\n      // If we have saved settings, use them\n      if (savedSettings) {\n        try {\n          const parsedSettings = JSON.parse(savedSettings);\n          if (typeof parsedSettings.enabled === 'boolean') data.enabled = parsedSettings.enabled;\n          if (typeof parsedSettings.sendOnNewScholarship === 'boolean') data.sendOnNewScholarship = parsedSettings.sendOnNewScholarship;\n          if (typeof parsedSettings.sendOnScholarshipUpdate === 'boolean') data.sendOnScholarshipUpdate = parsedSettings.sendOnScholarshipUpdate;\n          if (typeof parsedSettings.emailTemplate === 'string') data.emailTemplate = parsedSettings.emailTemplate;\n          if (typeof parsedSettings.frequency === 'string') data.frequency = parsedSettings.frequency as any;\n          if (parsedSettings.lastSent) data.lastSent = parsedSettings.lastSent;\n        } catch (e) {\n          console.error('Error parsing saved settings:', e);\n        }\n      }\n\n      setSettings(data);\n      form.setFieldsValue(data);\n      console.log('Notification settings loaded:', data);\n    } catch (error) {\n      console.error('Error fetching notification settings:', error);\n      message.error('Failed to load notification settings');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSave = async (values: any) => {\n    try {\n      setLoading(true);\n\n      // Save settings to localStorage\n      console.log('Saving notification settings:', values);\n      localStorage.setItem('email_notification_settings', JSON.stringify(values));\n\n      // Update state\n      setSettings(values);\n\n      // Simulate API delay\n      await new Promise(resolve => setTimeout(resolve, 500));\n\n      message.success('Notification settings saved successfully');\n    } catch (error) {\n      console.error('Error saving notification settings:', error);\n      message.error('Failed to save notification settings');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const sendTestEmail = async () => {\n    try {\n      setTestEmailLoading(true);\n\n      // Get current form values\n      const formValues = form.getFieldsValue();\n      console.log('Sending test email with settings:', formValues);\n\n      // Simulate API delay\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      // Show success message with template preview\n      const template = formValues.emailTemplate || 'New scholarship available: {{scholarshipTitle}} - Check it out now!';\n      const previewTemplate = template\n        .replace('{{scholarshipTitle}}', 'Example Scholarship')\n        .replace('{{scholarshipLink}}', 'https://example.com/scholarship/123')\n        .replace('{{scholarshipDeadline}}', '2024-12-31');\n\n      message.success(\n        <div>\n          <div>Test email sent <NAME_EMAIL></div>\n          <div style={{ marginTop: '8px', fontStyle: 'italic' }}>Preview: {previewTemplate}</div>\n        </div>\n      );\n    } catch (error) {\n      console.error('Error sending test email:', error);\n      message.error('Failed to send test email');\n    } finally {\n      setTestEmailLoading(false);\n    }\n  };\n\n  return (\n    <Card title={\n      <div className=\"flex items-center\">\n        <MailOutlined className=\"mr-2 text-blue-500\" />\n        <span>Email Notification Settings</span>\n      </div>\n    } className=\"shadow-md\">\n      <Form\n        form={form}\n        layout=\"vertical\"\n        initialValues={settings}\n        onFinish={handleSave}\n      >\n        <div className=\"mb-6\">\n          <Title level={5}>General Settings</Title>\n          <Text type=\"secondary\">Configure when and how email notifications are sent to subscribers</Text>\n        </div>\n\n        <Form.Item\n          name=\"enabled\"\n          valuePropName=\"checked\"\n          label=\"Enable Email Notifications\"\n        >\n          <Switch />\n        </Form.Item>\n\n        <Divider />\n\n        <div className=\"mb-6\">\n          <Title level={5}>Notification Triggers</Title>\n          <Text type=\"secondary\">Select which events should trigger email notifications</Text>\n        </div>\n\n        <Form.Item\n          name=\"sendOnNewScholarship\"\n          valuePropName=\"checked\"\n          label=\"New Scholarships\"\n        >\n          <Switch />\n        </Form.Item>\n\n        <Form.Item\n          name=\"sendOnScholarshipUpdate\"\n          valuePropName=\"checked\"\n          label=\"Scholarship Updates\"\n        >\n          <Switch />\n        </Form.Item>\n\n        <Form.Item\n          name=\"frequency\"\n          label=\"Notification Frequency\"\n        >\n          <Select>\n            <Option value=\"immediate\">Immediate</Option>\n            <Option value=\"daily\">Daily Digest</Option>\n            <Option value=\"weekly\">Weekly Digest</Option>\n          </Select>\n        </Form.Item>\n\n        <Divider />\n\n        <div className=\"mb-6\">\n          <Title level={5}>Email Template</Title>\n          <Text type=\"secondary\">\n            Customize the email template. You can use the following variables:\n            <ul className=\"list-disc ml-5 mt-2\">\n              <li>{'{{scholarshipTitle}}'} - The title of the scholarship</li>\n              <li>{'{{scholarshipLink}}'} - The link to the scholarship</li>\n              <li>{'{{scholarshipDeadline}}'} - The deadline of the scholarship</li>\n            </ul>\n          </Text>\n        </div>\n\n        <Form.Item\n          name=\"emailTemplate\"\n          label=\"Email Template\"\n        >\n          <TextArea rows={4} />\n        </Form.Item>\n\n        <Form.Item>\n          <Space>\n            <Button\n              type=\"primary\"\n              htmlType=\"submit\"\n              loading={loading}\n              icon={<SaveOutlined />}\n            >\n              Save Settings\n            </Button>\n            <Button\n              onClick={sendTestEmail}\n              loading={testEmailLoading}\n              icon={<SendOutlined />}\n            >\n              Send Test Email\n            </Button>\n          </Space>\n        </Form.Item>\n      </Form>\n    </Card>\n  );\n};\n\nexport default EmailNotificationSettings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,EAAEC,KAAK,EAAEC,UAAU,QAAQ,MAAM;AAErG,SAASC,YAAY,EAAEC,YAAY,EAAEC,YAAY,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7E,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGP,UAAU;AAClC,MAAM;EAAEQ;AAAO,CAAC,GAAGZ,MAAM;AACzB,MAAM;EAAEa;AAAS,CAAC,GAAGd,KAAK;AAW1B,MAAMe,yBAAmC,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChD,MAAM,CAACC,IAAI,CAAC,GAAGpB,IAAI,CAACqB,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC2B,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM,CAAC6B,QAAQ,EAAEC,WAAW,CAAC,GAAG9B,QAAQ,CAAuB;IAC7D+B,OAAO,EAAE,KAAK;IACdC,oBAAoB,EAAE,IAAI;IAC1BC,uBAAuB,EAAE,KAAK;IAC9BC,aAAa,EAAE,qEAAqE;IACpFC,SAAS,EAAE,WAAW;IACtBC,QAAQ,EAAE;EACZ,CAAC,CAAC;;EAEF;;EAEAnC,SAAS,CAAC,MAAM;IACdoC,aAAa,CAAC,CAAC;IACf;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFX,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACAY,OAAO,CAACC,GAAG,CAAC,uCAAuC,CAAC;;MAEpD;MACA,MAAMC,aAAa,GAAGC,YAAY,CAACC,OAAO,CAAC,6BAA6B,CAAC;;MAEzE;MACA,MAAMC,IAA0B,GAAG;QACjCZ,OAAO,EAAE,KAAK;QACdC,oBAAoB,EAAE,IAAI;QAC1BC,uBAAuB,EAAE,KAAK;QAC9BC,aAAa,EAAE,qEAAqE;QACpFC,SAAS,EAAE,WAAW;QACtBC,QAAQ,EAAE;MACZ,CAAC;;MAED;MACA,IAAII,aAAa,EAAE;QACjB,IAAI;UACF,MAAMI,cAAc,GAAGC,IAAI,CAACC,KAAK,CAACN,aAAa,CAAC;UAChD,IAAI,OAAOI,cAAc,CAACb,OAAO,KAAK,SAAS,EAAEY,IAAI,CAACZ,OAAO,GAAGa,cAAc,CAACb,OAAO;UACtF,IAAI,OAAOa,cAAc,CAACZ,oBAAoB,KAAK,SAAS,EAAEW,IAAI,CAACX,oBAAoB,GAAGY,cAAc,CAACZ,oBAAoB;UAC7H,IAAI,OAAOY,cAAc,CAACX,uBAAuB,KAAK,SAAS,EAAEU,IAAI,CAACV,uBAAuB,GAAGW,cAAc,CAACX,uBAAuB;UACtI,IAAI,OAAOW,cAAc,CAACV,aAAa,KAAK,QAAQ,EAAES,IAAI,CAACT,aAAa,GAAGU,cAAc,CAACV,aAAa;UACvG,IAAI,OAAOU,cAAc,CAACT,SAAS,KAAK,QAAQ,EAAEQ,IAAI,CAACR,SAAS,GAAGS,cAAc,CAACT,SAAgB;UAClG,IAAIS,cAAc,CAACR,QAAQ,EAAEO,IAAI,CAACP,QAAQ,GAAGQ,cAAc,CAACR,QAAQ;QACtE,CAAC,CAAC,OAAOW,CAAC,EAAE;UACVT,OAAO,CAACU,KAAK,CAAC,+BAA+B,EAAED,CAAC,CAAC;QACnD;MACF;MAEAjB,WAAW,CAACa,IAAI,CAAC;MACjBpB,IAAI,CAAC0B,cAAc,CAACN,IAAI,CAAC;MACzBL,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEI,IAAI,CAAC;IACpD,CAAC,CAAC,OAAOK,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7DxC,OAAO,CAACwC,KAAK,CAAC,sCAAsC,CAAC;IACvD,CAAC,SAAS;MACRtB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwB,UAAU,GAAG,MAAOC,MAAW,IAAK;IACxC,IAAI;MACFzB,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACAY,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEY,MAAM,CAAC;MACpDV,YAAY,CAACW,OAAO,CAAC,6BAA6B,EAAEP,IAAI,CAACQ,SAAS,CAACF,MAAM,CAAC,CAAC;;MAE3E;MACArB,WAAW,CAACqB,MAAM,CAAC;;MAEnB;MACA,MAAM,IAAIG,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;MAEtD/C,OAAO,CAACiD,OAAO,CAAC,0CAA0C,CAAC;IAC7D,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC3DxC,OAAO,CAACwC,KAAK,CAAC,sCAAsC,CAAC;IACvD,CAAC,SAAS;MACRtB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMgC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF9B,mBAAmB,CAAC,IAAI,CAAC;;MAEzB;MACA,MAAM+B,UAAU,GAAGpC,IAAI,CAACqC,cAAc,CAAC,CAAC;MACxCtB,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEoB,UAAU,CAAC;;MAE5D;MACA,MAAM,IAAIL,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;MAEvD;MACA,MAAMM,QAAQ,GAAGF,UAAU,CAACzB,aAAa,IAAI,qEAAqE;MAClH,MAAM4B,eAAe,GAAGD,QAAQ,CAC7BE,OAAO,CAAC,sBAAsB,EAAE,qBAAqB,CAAC,CACtDA,OAAO,CAAC,qBAAqB,EAAE,qCAAqC,CAAC,CACrEA,OAAO,CAAC,yBAAyB,EAAE,YAAY,CAAC;MAEnDvD,OAAO,CAACiD,OAAO,cACbzC,OAAA;QAAAgD,QAAA,gBACEhD,OAAA;UAAAgD,QAAA,EAAK;QAAiD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eAC5DpD,OAAA;UAAKqD,KAAK,EAAE;YAAEC,SAAS,EAAE,KAAK;YAAEC,SAAS,EAAE;UAAS,CAAE;UAAAP,QAAA,GAAC,WAAS,EAACF,eAAe;QAAA;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpF,CACP,CAAC;IACH,CAAC,CAAC,OAAOpB,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDxC,OAAO,CAACwC,KAAK,CAAC,2BAA2B,CAAC;IAC5C,CAAC,SAAS;MACRpB,mBAAmB,CAAC,KAAK,CAAC;IAC5B;EACF,CAAC;EAED,oBACEZ,OAAA,CAACd,IAAI;IAACsE,KAAK,eACTxD,OAAA;MAAKyD,SAAS,EAAC,mBAAmB;MAAAT,QAAA,gBAChChD,OAAA,CAACJ,YAAY;QAAC6D,SAAS,EAAC;MAAoB;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/CpD,OAAA;QAAAgD,QAAA,EAAM;MAA2B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CACN;IAACK,SAAS,EAAC,WAAW;IAAAT,QAAA,eACrBhD,OAAA,CAACb,IAAI;MACHoB,IAAI,EAAEA,IAAK;MACXmD,MAAM,EAAC,UAAU;MACjBC,aAAa,EAAE9C,QAAS;MACxB+C,QAAQ,EAAE1B,UAAW;MAAAc,QAAA,gBAErBhD,OAAA;QAAKyD,SAAS,EAAC,MAAM;QAAAT,QAAA,gBACnBhD,OAAA,CAACC,KAAK;UAAC4D,KAAK,EAAE,CAAE;UAAAb,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACzCpD,OAAA,CAACE,IAAI;UAAC4D,IAAI,EAAC,WAAW;UAAAd,QAAA,EAAC;QAAkE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7F,CAAC,eAENpD,OAAA,CAACb,IAAI,CAAC4E,IAAI;QACRC,IAAI,EAAC,SAAS;QACdC,aAAa,EAAC,SAAS;QACvBC,KAAK,EAAC,4BAA4B;QAAAlB,QAAA,eAElChD,OAAA,CAACZ,MAAM;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEZpD,OAAA,CAACP,OAAO;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEXpD,OAAA;QAAKyD,SAAS,EAAC,MAAM;QAAAT,QAAA,gBACnBhD,OAAA,CAACC,KAAK;UAAC4D,KAAK,EAAE,CAAE;UAAAb,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC9CpD,OAAA,CAACE,IAAI;UAAC4D,IAAI,EAAC,WAAW;UAAAd,QAAA,EAAC;QAAsD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjF,CAAC,eAENpD,OAAA,CAACb,IAAI,CAAC4E,IAAI;QACRC,IAAI,EAAC,sBAAsB;QAC3BC,aAAa,EAAC,SAAS;QACvBC,KAAK,EAAC,kBAAkB;QAAAlB,QAAA,eAExBhD,OAAA,CAACZ,MAAM;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEZpD,OAAA,CAACb,IAAI,CAAC4E,IAAI;QACRC,IAAI,EAAC,yBAAyB;QAC9BC,aAAa,EAAC,SAAS;QACvBC,KAAK,EAAC,qBAAqB;QAAAlB,QAAA,eAE3BhD,OAAA,CAACZ,MAAM;UAAA6D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAEZpD,OAAA,CAACb,IAAI,CAAC4E,IAAI;QACRC,IAAI,EAAC,WAAW;QAChBE,KAAK,EAAC,wBAAwB;QAAAlB,QAAA,eAE9BhD,OAAA,CAACT,MAAM;UAAAyD,QAAA,gBACLhD,OAAA,CAACG,MAAM;YAACgE,KAAK,EAAC,WAAW;YAAAnB,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC5CpD,OAAA,CAACG,MAAM;YAACgE,KAAK,EAAC,OAAO;YAAAnB,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC3CpD,OAAA,CAACG,MAAM;YAACgE,KAAK,EAAC,QAAQ;YAAAnB,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CAAC,eAEZpD,OAAA,CAACP,OAAO;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEXpD,OAAA;QAAKyD,SAAS,EAAC,MAAM;QAAAT,QAAA,gBACnBhD,OAAA,CAACC,KAAK;UAAC4D,KAAK,EAAE,CAAE;UAAAb,QAAA,EAAC;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eACvCpD,OAAA,CAACE,IAAI;UAAC4D,IAAI,EAAC,WAAW;UAAAd,QAAA,GAAC,oEAErB,eAAAhD,OAAA;YAAIyD,SAAS,EAAC,qBAAqB;YAAAT,QAAA,gBACjChD,OAAA;cAAAgD,QAAA,GAAK,sBAAsB,EAAC,iCAA+B;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAChEpD,OAAA;cAAAgD,QAAA,GAAK,qBAAqB,EAAC,gCAA8B;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC9DpD,OAAA;cAAAgD,QAAA,GAAK,yBAAyB,EAAC,oCAAkC;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAENpD,OAAA,CAACb,IAAI,CAAC4E,IAAI;QACRC,IAAI,EAAC,eAAe;QACpBE,KAAK,EAAC,gBAAgB;QAAAlB,QAAA,eAEtBhD,OAAA,CAACI,QAAQ;UAACgE,IAAI,EAAE;QAAE;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAEZpD,OAAA,CAACb,IAAI,CAAC4E,IAAI;QAAAf,QAAA,eACRhD,OAAA,CAACN,KAAK;UAAAsD,QAAA,gBACJhD,OAAA,CAACX,MAAM;YACLyE,IAAI,EAAC,SAAS;YACdO,QAAQ,EAAC,QAAQ;YACjB5D,OAAO,EAAEA,OAAQ;YACjB6D,IAAI,eAAEtE,OAAA,CAACH,YAAY;cAAAoD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACxB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpD,OAAA,CAACX,MAAM;YACLkF,OAAO,EAAE7B,aAAc;YACvBjC,OAAO,EAAEE,gBAAiB;YAC1B2D,IAAI,eAAEtE,OAAA,CAACF,YAAY;cAAAmD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAJ,QAAA,EACxB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEX,CAAC;AAAC9C,EAAA,CAhOID,yBAAmC;EAAA,QACxBlB,IAAI,CAACqB,OAAO;AAAA;AAAAgE,EAAA,GADvBnE,yBAAmC;AAkOzC,eAAeA,yBAAyB;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}