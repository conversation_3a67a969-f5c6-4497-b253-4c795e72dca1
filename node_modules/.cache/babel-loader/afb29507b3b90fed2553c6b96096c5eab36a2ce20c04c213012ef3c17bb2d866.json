{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/AuthMigrationNotice.tsx\";\nimport React from 'react';\nimport { Alert } from 'antd';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AuthMigrationNotice = () => {\n  return /*#__PURE__*/_jsxDEV(Alert, {\n    message: \"Authentication Migration Notice\",\n    description: \"The application is currently migrating from localStorage-based authentication to HTTP-only cookies for enhanced security.\",\n    type: \"info\",\n    showIcon: true,\n    style: {\n      margin: '16px 0'\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 6,\n    columnNumber: 5\n  }, this);\n};\n_c = AuthMigrationNotice;\nexport default AuthMigrationNotice;\nvar _c;\n$RefreshReg$(_c, \"AuthMigrationNotice\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "AuthMigrationNotice", "message", "description", "type", "showIcon", "style", "margin", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/AuthMigrationNotice.tsx"], "sourcesContent": ["import React from 'react';\nimport { Alert } from 'antd';\n\nconst AuthMigrationNotice: React.FC = () => {\n  return (\n    <Alert\n      message=\"Authentication Migration Notice\"\n      description=\"The application is currently migrating from localStorage-based authentication to HTTP-only cookies for enhanced security.\"\n      type=\"info\"\n      showIcon\n      style={{ margin: '16px 0' }}\n    />\n  );\n};\n\nexport default AuthMigrationNotice;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,KAAK,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7B,MAAMC,mBAA6B,GAAGA,CAAA,KAAM;EAC1C,oBACED,OAAA,CAACF,KAAK;IACJI,OAAO,EAAC,iCAAiC;IACzCC,WAAW,EAAC,2HAA2H;IACvIC,IAAI,EAAC,MAAM;IACXC,QAAQ;IACRC,KAAK,EAAE;MAAEC,MAAM,EAAE;IAAS;EAAE;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7B,CAAC;AAEN,CAAC;AAACC,EAAA,GAVIX,mBAA6B;AAYnC,eAAeA,mBAAmB;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}