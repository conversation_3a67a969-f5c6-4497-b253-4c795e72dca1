{"ast": null, "code": "/**\n * Utility functions for clearing localStorage and sessionStorage\n */\n\nexport const clearAllLocalStorage = () => {\n  try {\n    localStorage.clear();\n    console.log('All localStorage data cleared');\n    return true;\n  } catch (error) {\n    console.error('Error clearing localStorage:', error);\n    return false;\n  }\n};\nexport const clearSelectedLocalStorage = keys => {\n  try {\n    keys.forEach(key => {\n      localStorage.removeItem(key);\n    });\n    console.log('Selected localStorage items cleared:', keys);\n    return true;\n  } catch (error) {\n    console.error('Error clearing selected localStorage items:', error);\n    return false;\n  }\n};\nexport const clearAllSessionStorage = () => {\n  try {\n    sessionStorage.clear();\n    console.log('All sessionStorage data cleared');\n  } catch (error) {\n    console.error('Error clearing sessionStorage:', error);\n  }\n};\nexport const clearSelectedSessionStorage = keys => {\n  try {\n    keys.forEach(key => {\n      sessionStorage.removeItem(key);\n    });\n    console.log('Selected sessionStorage items cleared:', keys);\n  } catch (error) {\n    console.error('Error clearing selected sessionStorage items:', error);\n  }\n};", "map": {"version": 3, "names": ["clearAllLocalStorage", "localStorage", "clear", "console", "log", "error", "clearSelectedLocalStorage", "keys", "for<PERSON>ach", "key", "removeItem", "clearAllSessionStorage", "sessionStorage", "clearSelectedSessionStorage"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/clearStorage.ts"], "sourcesContent": ["/**\n * Utility functions for clearing localStorage and sessionStorage\n */\n\nexport const clearAllLocalStorage = (): boolean => {\n  try {\n    localStorage.clear();\n    console.log('All localStorage data cleared');\n    return true;\n  } catch (error) {\n    console.error('Error clearing localStorage:', error);\n    return false;\n  }\n};\n\nexport const clearSelectedLocalStorage = (keys: string[]): boolean => {\n  try {\n    keys.forEach(key => {\n      localStorage.removeItem(key);\n    });\n    console.log('Selected localStorage items cleared:', keys);\n    return true;\n  } catch (error) {\n    console.error('Error clearing selected localStorage items:', error);\n    return false;\n  }\n};\n\nexport const clearAllSessionStorage = (): void => {\n  try {\n    sessionStorage.clear();\n    console.log('All sessionStorage data cleared');\n  } catch (error) {\n    console.error('Error clearing sessionStorage:', error);\n  }\n};\n\nexport const clearSelectedSessionStorage = (keys: string[]): void => {\n  try {\n    keys.forEach(key => {\n      sessionStorage.removeItem(key);\n    });\n    console.log('Selected sessionStorage items cleared:', keys);\n  } catch (error) {\n    console.error('Error clearing selected sessionStorage items:', error);\n  }\n};\n"], "mappings": "AAAA;AACA;AACA;;AAEA,OAAO,MAAMA,oBAAoB,GAAGA,CAAA,KAAe;EACjD,IAAI;IACFC,YAAY,CAACC,KAAK,CAAC,CAAC;IACpBC,OAAO,CAACC,GAAG,CAAC,+BAA+B,CAAC;IAC5C,OAAO,IAAI;EACb,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACpD,OAAO,KAAK;EACd;AACF,CAAC;AAED,OAAO,MAAMC,yBAAyB,GAAIC,IAAc,IAAc;EACpE,IAAI;IACFA,IAAI,CAACC,OAAO,CAACC,GAAG,IAAI;MAClBR,YAAY,CAACS,UAAU,CAACD,GAAG,CAAC;IAC9B,CAAC,CAAC;IACFN,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEG,IAAI,CAAC;IACzD,OAAO,IAAI;EACb,CAAC,CAAC,OAAOF,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,6CAA6C,EAAEA,KAAK,CAAC;IACnE,OAAO,KAAK;EACd;AACF,CAAC;AAED,OAAO,MAAMM,sBAAsB,GAAGA,CAAA,KAAY;EAChD,IAAI;IACFC,cAAc,CAACV,KAAK,CAAC,CAAC;IACtBC,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;EAChD,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;EACxD;AACF,CAAC;AAED,OAAO,MAAMQ,2BAA2B,GAAIN,IAAc,IAAW;EACnE,IAAI;IACFA,IAAI,CAACC,OAAO,CAACC,GAAG,IAAI;MAClBG,cAAc,CAACF,UAAU,CAACD,GAAG,CAAC;IAChC,CAAC,CAAC;IACFN,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAEG,IAAI,CAAC;EAC7D,CAAC,CAAC,OAAOF,KAAK,EAAE;IACdF,OAAO,CAACE,KAAK,CAAC,+CAA+C,EAAEA,KAAK,CAAC;EACvE;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}