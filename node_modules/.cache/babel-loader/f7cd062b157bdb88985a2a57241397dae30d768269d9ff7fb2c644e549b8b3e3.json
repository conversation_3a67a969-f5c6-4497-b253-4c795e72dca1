{"ast": null, "code": "import axios from 'axios';\nimport { getBoolEnv } from '../utils/envValidator';\n\n/**\n * Secure API service using HTTP-only cookies for authentication\n * This is a more secure approach than using localStorage for tokens\n */\nclass SecureApiService {\n  constructor() {\n    this.api = void 0;\n    this.csrfToken = null;\n    // Create axios instance with default configuration\n    const baseURL = 'http://localhost:5000'; // Hardcode the URL for testing\n    console.log('Initializing secure API service with base URL:', baseURL);\n    this.api = axios.create({\n      baseURL: `${baseURL}/api`,\n      timeout: 15000,\n      // 15 seconds\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      withCredentials: true // Important for HTTP-only cookies\n    });\n\n    // Set up interceptors\n    this.setupInterceptors();\n\n    // Test the API connection (non-blocking)\n    this.testApiConnection();\n    console.log('Secure API service initialized with withCredentials:', true);\n  }\n\n  /**\n   * Test the API connection\n   * This is a helper method to diagnose connection issues\n   */\n  async testApiConnection() {\n    try {\n      console.log('Testing API connection...');\n      const response = await fetch('http://localhost:5000/api/health');\n      const data = await response.json();\n      console.log('API connection test successful:', data);\n    } catch (error) {\n      console.error('API connection test failed:', error);\n    }\n  }\n\n  /**\n   * Set up request and response interceptors\n   */\n  setupInterceptors() {\n    // Request interceptor for adding CSRF token\n    this.api.interceptors.request.use(config => {\n      // Add CSRF token if available\n      if (this.csrfToken) {\n        config.headers = config.headers || {};\n        config.headers['X-CSRF-Token'] = this.csrfToken;\n      }\n\n      // Add additional headers if needed\n      if (getBoolEnv('REACT_APP_DEBUG_MODE', false)) {\n        config.headers = config.headers || {};\n        config.headers['X-Debug-Mode'] = 'true';\n      }\n      return config;\n    }, error => {\n      // Log request errors in development\n      if (process.env.NODE_ENV === 'development') {\n        console.error('Request error:', error);\n      }\n      return Promise.reject(error);\n    });\n\n    // Response interceptor for handling common response patterns and errors\n    this.api.interceptors.response.use(response => {\n      // Store CSRF token if present in response\n      if (response.data && response.data.csrfToken) {\n        this.csrfToken = response.data.csrfToken;\n      }\n      return response;\n    }, error => {\n      // Handle 401 Unauthorized errors (token expired or invalid)\n      if (error.response && error.response.status === 401) {\n        // Redirect to login page if not already there\n        if (!window.location.pathname.includes('/login') && !window.location.pathname.includes('/secure-login')) {\n          console.log('Session expired. Redirecting to login page...');\n          window.location.href = '/admin/secure-login';\n        }\n      }\n\n      // Log all errors in development\n      if (process.env.NODE_ENV === 'development') {\n        console.error('Response error:', error);\n        if (error.response) {\n          console.error('Error data:', error.response.data);\n          console.error('Error status:', error.response.status);\n        } else if (error.request) {\n          console.error('No response received:', error.request);\n        } else {\n          console.error('Error setting up request:', error.message);\n        }\n      }\n      return Promise.reject(error);\n    });\n  }\n\n  /**\n   * Set CSRF token manually\n   * @param token CSRF token\n   */\n  setCsrfToken(token) {\n    this.csrfToken = token;\n  }\n\n  /**\n   * Get CSRF token\n   * @returns CSRF token\n   */\n  getCsrfToken() {\n    return this.csrfToken;\n  }\n\n  /**\n   * Make a GET request\n   * @param url - The URL to request\n   * @param config - Optional axios config\n   * @returns Promise with the response data\n   */\n  async get(url, config) {\n    const response = await this.api.get(url, config);\n    return response.data;\n  }\n\n  /**\n   * Make a POST request\n   * @param url - The URL to request\n   * @param data - The data to send\n   * @param config - Optional axios config\n   * @returns Promise with the response data\n   */\n  async post(url, data, config) {\n    const response = await this.api.post(url, data, config);\n    return response.data;\n  }\n\n  /**\n   * Make a PUT request\n   * @param url - The URL to request\n   * @param data - The data to send\n   * @param config - Optional axios config\n   * @returns Promise with the response data\n   */\n  async put(url, data, config) {\n    const response = await this.api.put(url, data, config);\n    return response.data;\n  }\n\n  /**\n   * Make a DELETE request\n   * @param url - The URL to request\n   * @param config - Optional axios config\n   * @returns Promise with the response data\n   */\n  async delete(url, config) {\n    const response = await this.api.delete(url, config);\n    return response.data;\n  }\n\n  /**\n   * Upload a file with multipart/form-data\n   * @param url - The URL to request\n   * @param formData - The FormData object with file and other data\n   * @param config - Optional axios config\n   * @returns Promise with the response data\n   */\n  async upload(url, formData, config) {\n    const response = await this.api.post(url, formData, {\n      ...config,\n      headers: {\n        ...(config === null || config === void 0 ? void 0 : config.headers),\n        'Content-Type': 'multipart/form-data'\n      }\n    });\n    return response.data;\n  }\n\n  /**\n   * Login a user\n   * @param email User email\n   * @param password User password\n   * @returns Promise with the response data\n   */\n  async login(email, password) {\n    return this.post('/auth/login', {\n      email,\n      password\n    });\n  }\n\n  /**\n   * Login an admin\n   * @param email Admin email\n   * @param password Admin password\n   * @returns Promise with the response data\n   */\n  async adminLogin(email, password) {\n    console.log('Attempting admin login with:', email);\n    try {\n      // Test backend connectivity first\n      try {\n        const healthResponse = await this.api.get('/health', {\n          timeout: 5000\n        });\n        console.log('Backend health check successful:', healthResponse.data);\n      } catch (healthError) {\n        console.error('Backend health check failed:', healthError);\n        throw new Error('Backend server is not accessible');\n      }\n\n      // Add more detailed logging\n      console.log('Making request to /admin/login with credentials');\n      console.log('API base URL:', this.api.defaults.baseURL);\n      console.log('Request config:', {\n        withCredentials: this.api.defaults.withCredentials,\n        timeout: this.api.defaults.timeout\n      });\n\n      // Make the request with explicit configuration\n      const response = await this.api.post('/admin/login', {\n        email,\n        password\n      }, {\n        withCredentials: true,\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        timeout: 30000 // Increase timeout for debugging\n      });\n      console.log('Admin login response status:', response.status);\n      console.log('Admin login response data:', response.data);\n      const result = response.data;\n\n      // Validate the response structure\n      if (!result.success) {\n        throw new Error(result.message || 'Login failed');\n      }\n      if (!result.data || !result.data.admin) {\n        throw new Error('Invalid response structure from server');\n      }\n      console.log('Admin login successful:', result.data.admin);\n      return result;\n    } catch (error) {\n      console.error('Admin login failed:', error);\n\n      // Provide more specific error messages\n      if (error.response) {\n        console.error('Response error:', {\n          status: error.response.status,\n          data: error.response.data,\n          headers: error.response.headers\n        });\n        if (error.response.status === 401) {\n          throw new Error('Invalid email or password');\n        } else if (error.response.status === 423) {\n          throw new Error('Account is locked. Please try again later.');\n        } else if (error.response.status >= 500) {\n          throw new Error('Server error. Please try again later.');\n        } else {\n          var _error$response$data;\n          throw new Error(((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.message) || 'Login failed');\n        }\n      } else if (error.request) {\n        console.error('Request error:', error.request);\n        throw new Error('Unable to connect to server. Please check your connection.');\n      } else {\n        console.error('Error:', error.message);\n        throw new Error(error.message || 'An unexpected error occurred');\n      }\n    }\n  }\n\n  /**\n   * Logout a user or admin\n   * @returns Promise with the response data\n   */\n  async logout() {\n    try {\n      // Try admin logout first\n      console.log('Attempting admin logout...');\n      const response = await this.post('/admin/logout');\n      console.log('Admin logout response:', response);\n      return response;\n    } catch (error) {\n      console.log('Admin logout failed, trying user logout...');\n      // If admin logout fails, try user logout\n      return this.post('/auth/logout');\n    }\n  }\n\n  /**\n   * Get current user profile\n   * @returns Promise with the response data\n   */\n  async getProfile() {\n    return this.get('/auth/profile');\n  }\n\n  /**\n   * Get current admin profile\n   * @returns Promise with the response data\n   */\n  async getAdminProfile() {\n    console.log('Fetching admin profile');\n    try {\n      // Add more detailed logging\n      console.log('Making request to /admin/current with cookies:', document.cookie);\n\n      // Make the request with explicit configuration\n      const response = await this.api.get('/admin/current', {\n        withCredentials: true\n      });\n      console.log('Admin profile response:', response);\n      const result = response.data;\n      console.log('Admin profile fetch successful:', result);\n      return result;\n    } catch (error) {\n      var _error$response;\n      console.error('Admin profile fetch failed:', error);\n      console.error('Error details:', ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data) || error.message || 'Unknown error');\n      throw error;\n    }\n  }\n\n  // Messages API\n  /**\n   * Get all messages\n   */\n  async getMessages() {\n    try {\n      const response = await this.api.get('/messages', {\n        withCredentials: true\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching messages:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Update message status\n   */\n  async updateMessageStatus(id, status) {\n    try {\n      const response = await this.api.put(`/messages/${id}`, {\n        status\n      }, {\n        withCredentials: true\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Error updating message status:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Delete a message\n   */\n  async deleteMessage(id) {\n    try {\n      const response = await this.api.delete(`/messages/${id}`, {\n        withCredentials: true\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Error deleting message:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Reply to a message\n   */\n  async replyToMessage(id, reply) {\n    try {\n      const response = await this.api.post(`/messages/${id}/reply`, {\n        reply\n      }, {\n        withCredentials: true\n      });\n      return response.data;\n    } catch (error) {\n      console.error('Error replying to message:', error);\n      throw error;\n    }\n  }\n}\n\n// Create a singleton instance\nconst secureApiService = new SecureApiService();\n\n// Export messages API for compatibility\nexport const messagesAPI = {\n  getMessages: () => secureApiService.getMessages(),\n  updateMessageStatus: (id, status) => secureApiService.updateMessageStatus(id, status),\n  deleteMessage: id => secureApiService.deleteMessage(id),\n  replyToMessage: (id, reply) => secureApiService.replyToMessage(id, reply)\n};\nexport default secureApiService;", "map": {"version": 3, "names": ["axios", "getBoolEnv", "SecureApiService", "constructor", "api", "csrfToken", "baseURL", "console", "log", "create", "timeout", "headers", "withCredentials", "setupInterceptors", "testApiConnection", "response", "fetch", "data", "json", "error", "interceptors", "request", "use", "config", "process", "env", "NODE_ENV", "Promise", "reject", "status", "window", "location", "pathname", "includes", "href", "message", "setCsrfToken", "token", "getCsrfToken", "get", "url", "post", "put", "delete", "upload", "formData", "login", "email", "password", "adminLogin", "healthResponse", "healthError", "Error", "defaults", "result", "success", "admin", "_error$response$data", "logout", "getProfile", "getAdminProfile", "document", "cookie", "_error$response", "getMessages", "updateMessageStatus", "id", "deleteMessage", "replyToMessage", "reply", "secureApiService", "messagesAPI"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/secureApiService.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError, InternalAxiosRequestConfig } from 'axios';\nimport { getBoolEnv } from '../utils/envValidator';\n\n/**\n * Secure API service using HTTP-only cookies for authentication\n * This is a more secure approach than using localStorage for tokens\n */\nclass SecureApiService {\n  private api: AxiosInstance;\n  private csrfToken: string | null = null;\n\n  constructor() {\n    // Create axios instance with default configuration\n    const baseURL = 'http://localhost:5000'; // Hardcode the URL for testing\n    console.log('Initializing secure API service with base URL:', baseURL);\n\n    this.api = axios.create({\n      baseURL: `${baseURL}/api`,\n      timeout: 15000, // 15 seconds\n      headers: {\n        'Content-Type': 'application/json',\n      },\n      withCredentials: true, // Important for HTTP-only cookies\n    });\n\n    // Set up interceptors\n    this.setupInterceptors();\n\n    // Test the API connection (non-blocking)\n    this.testApiConnection();\n\n    console.log('Secure API service initialized with withCredentials:', true);\n  }\n\n  /**\n   * Test the API connection\n   * This is a helper method to diagnose connection issues\n   */\n  private async testApiConnection() {\n    try {\n      console.log('Testing API connection...');\n      const response = await fetch('http://localhost:5000/api/health');\n      const data = await response.json();\n      console.log('API connection test successful:', data);\n    } catch (error) {\n      console.error('API connection test failed:', error);\n    }\n  }\n\n  /**\n   * Set up request and response interceptors\n   */\n  private setupInterceptors() {\n    // Request interceptor for adding CSRF token\n    this.api.interceptors.request.use(\n      (config: InternalAxiosRequestConfig): InternalAxiosRequestConfig => {\n        // Add CSRF token if available\n        if (this.csrfToken) {\n          config.headers = config.headers || {};\n          config.headers['X-CSRF-Token'] = this.csrfToken;\n        }\n\n        // Add additional headers if needed\n        if (getBoolEnv('REACT_APP_DEBUG_MODE', false)) {\n          config.headers = config.headers || {};\n          config.headers['X-Debug-Mode'] = 'true';\n        }\n\n        return config;\n      },\n      (error: AxiosError): Promise<AxiosError> => {\n        // Log request errors in development\n        if (process.env.NODE_ENV === 'development') {\n          console.error('Request error:', error);\n        }\n        return Promise.reject(error);\n      }\n    );\n\n    // Response interceptor for handling common response patterns and errors\n    this.api.interceptors.response.use(\n      (response: AxiosResponse): AxiosResponse => {\n        // Store CSRF token if present in response\n        if (response.data && response.data.csrfToken) {\n          this.csrfToken = response.data.csrfToken;\n        }\n        return response;\n      },\n      (error: AxiosError): Promise<AxiosError> => {\n        // Handle 401 Unauthorized errors (token expired or invalid)\n        if (error.response && error.response.status === 401) {\n          // Redirect to login page if not already there\n          if (!window.location.pathname.includes('/login') && !window.location.pathname.includes('/secure-login')) {\n            console.log('Session expired. Redirecting to login page...');\n            window.location.href = '/admin/secure-login';\n          }\n        }\n\n        // Log all errors in development\n        if (process.env.NODE_ENV === 'development') {\n          console.error('Response error:', error);\n\n          if (error.response) {\n            console.error('Error data:', error.response.data);\n            console.error('Error status:', error.response.status);\n          } else if (error.request) {\n            console.error('No response received:', error.request);\n          } else {\n            console.error('Error setting up request:', error.message);\n          }\n        }\n\n        return Promise.reject(error);\n      }\n    );\n  }\n\n  /**\n   * Set CSRF token manually\n   * @param token CSRF token\n   */\n  setCsrfToken(token: string): void {\n    this.csrfToken = token;\n  }\n\n  /**\n   * Get CSRF token\n   * @returns CSRF token\n   */\n  getCsrfToken(): string | null {\n    return this.csrfToken;\n  }\n\n  /**\n   * Make a GET request\n   * @param url - The URL to request\n   * @param config - Optional axios config\n   * @returns Promise with the response data\n   */\n  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.api.get<T>(url, config);\n    return response.data;\n  }\n\n  /**\n   * Make a POST request\n   * @param url - The URL to request\n   * @param data - The data to send\n   * @param config - Optional axios config\n   * @returns Promise with the response data\n   */\n  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.api.post<T>(url, data, config);\n    return response.data;\n  }\n\n  /**\n   * Make a PUT request\n   * @param url - The URL to request\n   * @param data - The data to send\n   * @param config - Optional axios config\n   * @returns Promise with the response data\n   */\n  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.api.put<T>(url, data, config);\n    return response.data;\n  }\n\n  /**\n   * Make a DELETE request\n   * @param url - The URL to request\n   * @param config - Optional axios config\n   * @returns Promise with the response data\n   */\n  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.api.delete<T>(url, config);\n    return response.data;\n  }\n\n  /**\n   * Upload a file with multipart/form-data\n   * @param url - The URL to request\n   * @param formData - The FormData object with file and other data\n   * @param config - Optional axios config\n   * @returns Promise with the response data\n   */\n  async upload<T>(url: string, formData: FormData, config?: AxiosRequestConfig): Promise<T> {\n    const response = await this.api.post<T>(url, formData, {\n      ...config,\n      headers: {\n        ...config?.headers,\n        'Content-Type': 'multipart/form-data',\n      },\n    });\n    return response.data;\n  }\n\n  /**\n   * Login a user\n   * @param email User email\n   * @param password User password\n   * @returns Promise with the response data\n   */\n  async login(email: string, password: string): Promise<any> {\n    return this.post('/auth/login', { email, password });\n  }\n\n  /**\n   * Login an admin\n   * @param email Admin email\n   * @param password Admin password\n   * @returns Promise with the response data\n   */\n  async adminLogin(email: string, password: string): Promise<any> {\n    console.log('Attempting admin login with:', email);\n    try {\n      // Test backend connectivity first\n      try {\n        const healthResponse = await this.api.get('/health', { timeout: 5000 });\n        console.log('Backend health check successful:', healthResponse.data);\n      } catch (healthError) {\n        console.error('Backend health check failed:', healthError);\n        throw new Error('Backend server is not accessible');\n      }\n\n      // Add more detailed logging\n      console.log('Making request to /admin/login with credentials');\n      console.log('API base URL:', this.api.defaults.baseURL);\n      console.log('Request config:', {\n        withCredentials: this.api.defaults.withCredentials,\n        timeout: this.api.defaults.timeout\n      });\n\n      // Make the request with explicit configuration\n      const response = await this.api.post('/admin/login', { email, password }, {\n        withCredentials: true,\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        timeout: 30000 // Increase timeout for debugging\n      });\n\n      console.log('Admin login response status:', response.status);\n      console.log('Admin login response data:', response.data);\n\n      const result = response.data;\n\n      // Validate the response structure\n      if (!result.success) {\n        throw new Error(result.message || 'Login failed');\n      }\n\n      if (!result.data || !result.data.admin) {\n        throw new Error('Invalid response structure from server');\n      }\n\n      console.log('Admin login successful:', result.data.admin);\n      return result;\n    } catch (error: any) {\n      console.error('Admin login failed:', error);\n\n      // Provide more specific error messages\n      if (error.response) {\n        console.error('Response error:', {\n          status: error.response.status,\n          data: error.response.data,\n          headers: error.response.headers\n        });\n\n        if (error.response.status === 401) {\n          throw new Error('Invalid email or password');\n        } else if (error.response.status === 423) {\n          throw new Error('Account is locked. Please try again later.');\n        } else if (error.response.status >= 500) {\n          throw new Error('Server error. Please try again later.');\n        } else {\n          throw new Error(error.response.data?.message || 'Login failed');\n        }\n      } else if (error.request) {\n        console.error('Request error:', error.request);\n        throw new Error('Unable to connect to server. Please check your connection.');\n      } else {\n        console.error('Error:', error.message);\n        throw new Error(error.message || 'An unexpected error occurred');\n      }\n    }\n  }\n\n  /**\n   * Logout a user or admin\n   * @returns Promise with the response data\n   */\n  async logout(): Promise<any> {\n    try {\n      // Try admin logout first\n      console.log('Attempting admin logout...');\n      const response = await this.post('/admin/logout');\n      console.log('Admin logout response:', response);\n      return response;\n    } catch (error) {\n      console.log('Admin logout failed, trying user logout...');\n      // If admin logout fails, try user logout\n      return this.post('/auth/logout');\n    }\n  }\n\n  /**\n   * Get current user profile\n   * @returns Promise with the response data\n   */\n  async getProfile(): Promise<any> {\n    return this.get('/auth/profile');\n  }\n\n  /**\n   * Get current admin profile\n   * @returns Promise with the response data\n   */\n  async getAdminProfile(): Promise<any> {\n    console.log('Fetching admin profile');\n    try {\n      // Add more detailed logging\n      console.log('Making request to /admin/current with cookies:', document.cookie);\n\n      // Make the request with explicit configuration\n      const response = await this.api.get('/admin/current', {\n        withCredentials: true\n      });\n\n      console.log('Admin profile response:', response);\n\n      const result = response.data;\n      console.log('Admin profile fetch successful:', result);\n      return result;\n    } catch (error: any) {\n      console.error('Admin profile fetch failed:', error);\n      console.error('Error details:', error.response?.data || error.message || 'Unknown error');\n      throw error;\n    }\n  }\n\n  // Messages API\n  /**\n   * Get all messages\n   */\n  async getMessages(): Promise<any> {\n    try {\n      const response = await this.api.get('/messages', {\n        withCredentials: true,\n      });\n      return response.data;\n    } catch (error: any) {\n      console.error('Error fetching messages:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Update message status\n   */\n  async updateMessageStatus(id: number, status: string): Promise<any> {\n    try {\n      const response = await this.api.put(`/messages/${id}`, { status }, {\n        withCredentials: true,\n      });\n      return response.data;\n    } catch (error: any) {\n      console.error('Error updating message status:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Delete a message\n   */\n  async deleteMessage(id: number): Promise<any> {\n    try {\n      const response = await this.api.delete(`/messages/${id}`, {\n        withCredentials: true,\n      });\n      return response.data;\n    } catch (error: any) {\n      console.error('Error deleting message:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Reply to a message\n   */\n  async replyToMessage(id: number, reply: string): Promise<any> {\n    try {\n      const response = await this.api.post(`/messages/${id}/reply`, { reply }, {\n        withCredentials: true,\n      });\n      return response.data;\n    } catch (error: any) {\n      console.error('Error replying to message:', error);\n      throw error;\n    }\n  }\n}\n\n// Create a singleton instance\nconst secureApiService = new SecureApiService();\n\n// Export messages API for compatibility\nexport const messagesAPI = {\n  getMessages: () => secureApiService.getMessages(),\n  updateMessageStatus: (id: number, status: string) => secureApiService.updateMessageStatus(id, status),\n  deleteMessage: (id: number) => secureApiService.deleteMessage(id),\n  replyToMessage: (id: number, reply: string) => secureApiService.replyToMessage(id, reply),\n};\n\nexport default secureApiService;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAoG,OAAO;AACvH,SAASC,UAAU,QAAQ,uBAAuB;;AAElD;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,CAAC;EAIrBC,WAAWA,CAAA,EAAG;IAAA,KAHNC,GAAG;IAAA,KACHC,SAAS,GAAkB,IAAI;IAGrC;IACA,MAAMC,OAAO,GAAG,uBAAuB,CAAC,CAAC;IACzCC,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEF,OAAO,CAAC;IAEtE,IAAI,CAACF,GAAG,GAAGJ,KAAK,CAACS,MAAM,CAAC;MACtBH,OAAO,EAAE,GAAGA,OAAO,MAAM;MACzBI,OAAO,EAAE,KAAK;MAAE;MAChBC,OAAO,EAAE;QACP,cAAc,EAAE;MAClB,CAAC;MACDC,eAAe,EAAE,IAAI,CAAE;IACzB,CAAC,CAAC;;IAEF;IACA,IAAI,CAACC,iBAAiB,CAAC,CAAC;;IAExB;IACA,IAAI,CAACC,iBAAiB,CAAC,CAAC;IAExBP,OAAO,CAACC,GAAG,CAAC,sDAAsD,EAAE,IAAI,CAAC;EAC3E;;EAEA;AACF;AACA;AACA;EACE,MAAcM,iBAAiBA,CAAA,EAAG;IAChC,IAAI;MACFP,OAAO,CAACC,GAAG,CAAC,2BAA2B,CAAC;MACxC,MAAMO,QAAQ,GAAG,MAAMC,KAAK,CAAC,kCAAkC,CAAC;MAChE,MAAMC,IAAI,GAAG,MAAMF,QAAQ,CAACG,IAAI,CAAC,CAAC;MAClCX,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAES,IAAI,CAAC;IACtD,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdZ,OAAO,CAACY,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF;;EAEA;AACF;AACA;EACUN,iBAAiBA,CAAA,EAAG;IAC1B;IACA,IAAI,CAACT,GAAG,CAACgB,YAAY,CAACC,OAAO,CAACC,GAAG,CAC9BC,MAAkC,IAAiC;MAClE;MACA,IAAI,IAAI,CAAClB,SAAS,EAAE;QAClBkB,MAAM,CAACZ,OAAO,GAAGY,MAAM,CAACZ,OAAO,IAAI,CAAC,CAAC;QACrCY,MAAM,CAACZ,OAAO,CAAC,cAAc,CAAC,GAAG,IAAI,CAACN,SAAS;MACjD;;MAEA;MACA,IAAIJ,UAAU,CAAC,sBAAsB,EAAE,KAAK,CAAC,EAAE;QAC7CsB,MAAM,CAACZ,OAAO,GAAGY,MAAM,CAACZ,OAAO,IAAI,CAAC,CAAC;QACrCY,MAAM,CAACZ,OAAO,CAAC,cAAc,CAAC,GAAG,MAAM;MACzC;MAEA,OAAOY,MAAM;IACf,CAAC,EACAJ,KAAiB,IAA0B;MAC1C;MACA,IAAIK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;QAC1CnB,OAAO,CAACY,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;MACxC;MACA,OAAOQ,OAAO,CAACC,MAAM,CAACT,KAAK,CAAC;IAC9B,CACF,CAAC;;IAED;IACA,IAAI,CAACf,GAAG,CAACgB,YAAY,CAACL,QAAQ,CAACO,GAAG,CAC/BP,QAAuB,IAAoB;MAC1C;MACA,IAAIA,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACZ,SAAS,EAAE;QAC5C,IAAI,CAACA,SAAS,GAAGU,QAAQ,CAACE,IAAI,CAACZ,SAAS;MAC1C;MACA,OAAOU,QAAQ;IACjB,CAAC,EACAI,KAAiB,IAA0B;MAC1C;MACA,IAAIA,KAAK,CAACJ,QAAQ,IAAII,KAAK,CAACJ,QAAQ,CAACc,MAAM,KAAK,GAAG,EAAE;QACnD;QACA,IAAI,CAACC,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAACC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAACH,MAAM,CAACC,QAAQ,CAACC,QAAQ,CAACC,QAAQ,CAAC,eAAe,CAAC,EAAE;UACvG1B,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;UAC5DsB,MAAM,CAACC,QAAQ,CAACG,IAAI,GAAG,qBAAqB;QAC9C;MACF;;MAEA;MACA,IAAIV,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;QAC1CnB,OAAO,CAACY,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;QAEvC,IAAIA,KAAK,CAACJ,QAAQ,EAAE;UAClBR,OAAO,CAACY,KAAK,CAAC,aAAa,EAAEA,KAAK,CAACJ,QAAQ,CAACE,IAAI,CAAC;UACjDV,OAAO,CAACY,KAAK,CAAC,eAAe,EAAEA,KAAK,CAACJ,QAAQ,CAACc,MAAM,CAAC;QACvD,CAAC,MAAM,IAAIV,KAAK,CAACE,OAAO,EAAE;UACxBd,OAAO,CAACY,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAACE,OAAO,CAAC;QACvD,CAAC,MAAM;UACLd,OAAO,CAACY,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAACgB,OAAO,CAAC;QAC3D;MACF;MAEA,OAAOR,OAAO,CAACC,MAAM,CAACT,KAAK,CAAC;IAC9B,CACF,CAAC;EACH;;EAEA;AACF;AACA;AACA;EACEiB,YAAYA,CAACC,KAAa,EAAQ;IAChC,IAAI,CAAChC,SAAS,GAAGgC,KAAK;EACxB;;EAEA;AACF;AACA;AACA;EACEC,YAAYA,CAAA,EAAkB;IAC5B,OAAO,IAAI,CAACjC,SAAS;EACvB;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMkC,GAAGA,CAAIC,GAAW,EAAEjB,MAA2B,EAAc;IACjE,MAAMR,QAAQ,GAAG,MAAM,IAAI,CAACX,GAAG,CAACmC,GAAG,CAAIC,GAAG,EAAEjB,MAAM,CAAC;IACnD,OAAOR,QAAQ,CAACE,IAAI;EACtB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAMwB,IAAIA,CAAID,GAAW,EAAEvB,IAAU,EAAEM,MAA2B,EAAc;IAC9E,MAAMR,QAAQ,GAAG,MAAM,IAAI,CAACX,GAAG,CAACqC,IAAI,CAAID,GAAG,EAAEvB,IAAI,EAAEM,MAAM,CAAC;IAC1D,OAAOR,QAAQ,CAACE,IAAI;EACtB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAMyB,GAAGA,CAAIF,GAAW,EAAEvB,IAAU,EAAEM,MAA2B,EAAc;IAC7E,MAAMR,QAAQ,GAAG,MAAM,IAAI,CAACX,GAAG,CAACsC,GAAG,CAAIF,GAAG,EAAEvB,IAAI,EAAEM,MAAM,CAAC;IACzD,OAAOR,QAAQ,CAACE,IAAI;EACtB;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAM0B,MAAMA,CAAIH,GAAW,EAAEjB,MAA2B,EAAc;IACpE,MAAMR,QAAQ,GAAG,MAAM,IAAI,CAACX,GAAG,CAACuC,MAAM,CAAIH,GAAG,EAAEjB,MAAM,CAAC;IACtD,OAAOR,QAAQ,CAACE,IAAI;EACtB;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;EACE,MAAM2B,MAAMA,CAAIJ,GAAW,EAAEK,QAAkB,EAAEtB,MAA2B,EAAc;IACxF,MAAMR,QAAQ,GAAG,MAAM,IAAI,CAACX,GAAG,CAACqC,IAAI,CAAID,GAAG,EAAEK,QAAQ,EAAE;MACrD,GAAGtB,MAAM;MACTZ,OAAO,EAAE;QACP,IAAGY,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEZ,OAAO;QAClB,cAAc,EAAE;MAClB;IACF,CAAC,CAAC;IACF,OAAOI,QAAQ,CAACE,IAAI;EACtB;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAM6B,KAAKA,CAACC,KAAa,EAAEC,QAAgB,EAAgB;IACzD,OAAO,IAAI,CAACP,IAAI,CAAC,aAAa,EAAE;MAAEM,KAAK;MAAEC;IAAS,CAAC,CAAC;EACtD;;EAEA;AACF;AACA;AACA;AACA;AACA;EACE,MAAMC,UAAUA,CAACF,KAAa,EAAEC,QAAgB,EAAgB;IAC9DzC,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEuC,KAAK,CAAC;IAClD,IAAI;MACF;MACA,IAAI;QACF,MAAMG,cAAc,GAAG,MAAM,IAAI,CAAC9C,GAAG,CAACmC,GAAG,CAAC,SAAS,EAAE;UAAE7B,OAAO,EAAE;QAAK,CAAC,CAAC;QACvEH,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE0C,cAAc,CAACjC,IAAI,CAAC;MACtE,CAAC,CAAC,OAAOkC,WAAW,EAAE;QACpB5C,OAAO,CAACY,KAAK,CAAC,8BAA8B,EAAEgC,WAAW,CAAC;QAC1D,MAAM,IAAIC,KAAK,CAAC,kCAAkC,CAAC;MACrD;;MAEA;MACA7C,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;MAC9DD,OAAO,CAACC,GAAG,CAAC,eAAe,EAAE,IAAI,CAACJ,GAAG,CAACiD,QAAQ,CAAC/C,OAAO,CAAC;MACvDC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAE;QAC7BI,eAAe,EAAE,IAAI,CAACR,GAAG,CAACiD,QAAQ,CAACzC,eAAe;QAClDF,OAAO,EAAE,IAAI,CAACN,GAAG,CAACiD,QAAQ,CAAC3C;MAC7B,CAAC,CAAC;;MAEF;MACA,MAAMK,QAAQ,GAAG,MAAM,IAAI,CAACX,GAAG,CAACqC,IAAI,CAAC,cAAc,EAAE;QAAEM,KAAK;QAAEC;MAAS,CAAC,EAAE;QACxEpC,eAAe,EAAE,IAAI;QACrBD,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDD,OAAO,EAAE,KAAK,CAAC;MACjB,CAAC,CAAC;MAEFH,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEO,QAAQ,CAACc,MAAM,CAAC;MAC5DtB,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEO,QAAQ,CAACE,IAAI,CAAC;MAExD,MAAMqC,MAAM,GAAGvC,QAAQ,CAACE,IAAI;;MAE5B;MACA,IAAI,CAACqC,MAAM,CAACC,OAAO,EAAE;QACnB,MAAM,IAAIH,KAAK,CAACE,MAAM,CAACnB,OAAO,IAAI,cAAc,CAAC;MACnD;MAEA,IAAI,CAACmB,MAAM,CAACrC,IAAI,IAAI,CAACqC,MAAM,CAACrC,IAAI,CAACuC,KAAK,EAAE;QACtC,MAAM,IAAIJ,KAAK,CAAC,wCAAwC,CAAC;MAC3D;MAEA7C,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE8C,MAAM,CAACrC,IAAI,CAACuC,KAAK,CAAC;MACzD,OAAOF,MAAM;IACf,CAAC,CAAC,OAAOnC,KAAU,EAAE;MACnBZ,OAAO,CAACY,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;;MAE3C;MACA,IAAIA,KAAK,CAACJ,QAAQ,EAAE;QAClBR,OAAO,CAACY,KAAK,CAAC,iBAAiB,EAAE;UAC/BU,MAAM,EAAEV,KAAK,CAACJ,QAAQ,CAACc,MAAM;UAC7BZ,IAAI,EAAEE,KAAK,CAACJ,QAAQ,CAACE,IAAI;UACzBN,OAAO,EAAEQ,KAAK,CAACJ,QAAQ,CAACJ;QAC1B,CAAC,CAAC;QAEF,IAAIQ,KAAK,CAACJ,QAAQ,CAACc,MAAM,KAAK,GAAG,EAAE;UACjC,MAAM,IAAIuB,KAAK,CAAC,2BAA2B,CAAC;QAC9C,CAAC,MAAM,IAAIjC,KAAK,CAACJ,QAAQ,CAACc,MAAM,KAAK,GAAG,EAAE;UACxC,MAAM,IAAIuB,KAAK,CAAC,4CAA4C,CAAC;QAC/D,CAAC,MAAM,IAAIjC,KAAK,CAACJ,QAAQ,CAACc,MAAM,IAAI,GAAG,EAAE;UACvC,MAAM,IAAIuB,KAAK,CAAC,uCAAuC,CAAC;QAC1D,CAAC,MAAM;UAAA,IAAAK,oBAAA;UACL,MAAM,IAAIL,KAAK,CAAC,EAAAK,oBAAA,GAAAtC,KAAK,CAACJ,QAAQ,CAACE,IAAI,cAAAwC,oBAAA,uBAAnBA,oBAAA,CAAqBtB,OAAO,KAAI,cAAc,CAAC;QACjE;MACF,CAAC,MAAM,IAAIhB,KAAK,CAACE,OAAO,EAAE;QACxBd,OAAO,CAACY,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAACE,OAAO,CAAC;QAC9C,MAAM,IAAI+B,KAAK,CAAC,4DAA4D,CAAC;MAC/E,CAAC,MAAM;QACL7C,OAAO,CAACY,KAAK,CAAC,QAAQ,EAAEA,KAAK,CAACgB,OAAO,CAAC;QACtC,MAAM,IAAIiB,KAAK,CAACjC,KAAK,CAACgB,OAAO,IAAI,8BAA8B,CAAC;MAClE;IACF;EACF;;EAEA;AACF;AACA;AACA;EACE,MAAMuB,MAAMA,CAAA,EAAiB;IAC3B,IAAI;MACF;MACAnD,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;MACzC,MAAMO,QAAQ,GAAG,MAAM,IAAI,CAAC0B,IAAI,CAAC,eAAe,CAAC;MACjDlC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEO,QAAQ,CAAC;MAC/C,OAAOA,QAAQ;IACjB,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdZ,OAAO,CAACC,GAAG,CAAC,4CAA4C,CAAC;MACzD;MACA,OAAO,IAAI,CAACiC,IAAI,CAAC,cAAc,CAAC;IAClC;EACF;;EAEA;AACF;AACA;AACA;EACE,MAAMkB,UAAUA,CAAA,EAAiB;IAC/B,OAAO,IAAI,CAACpB,GAAG,CAAC,eAAe,CAAC;EAClC;;EAEA;AACF;AACA;AACA;EACE,MAAMqB,eAAeA,CAAA,EAAiB;IACpCrD,OAAO,CAACC,GAAG,CAAC,wBAAwB,CAAC;IACrC,IAAI;MACF;MACAD,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAEqD,QAAQ,CAACC,MAAM,CAAC;;MAE9E;MACA,MAAM/C,QAAQ,GAAG,MAAM,IAAI,CAACX,GAAG,CAACmC,GAAG,CAAC,gBAAgB,EAAE;QACpD3B,eAAe,EAAE;MACnB,CAAC,CAAC;MAEFL,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEO,QAAQ,CAAC;MAEhD,MAAMuC,MAAM,GAAGvC,QAAQ,CAACE,IAAI;MAC5BV,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE8C,MAAM,CAAC;MACtD,OAAOA,MAAM;IACf,CAAC,CAAC,OAAOnC,KAAU,EAAE;MAAA,IAAA4C,eAAA;MACnBxD,OAAO,CAACY,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDZ,OAAO,CAACY,KAAK,CAAC,gBAAgB,EAAE,EAAA4C,eAAA,GAAA5C,KAAK,CAACJ,QAAQ,cAAAgD,eAAA,uBAAdA,eAAA,CAAgB9C,IAAI,KAAIE,KAAK,CAACgB,OAAO,IAAI,eAAe,CAAC;MACzF,MAAMhB,KAAK;IACb;EACF;;EAEA;EACA;AACF;AACA;EACE,MAAM6C,WAAWA,CAAA,EAAiB;IAChC,IAAI;MACF,MAAMjD,QAAQ,GAAG,MAAM,IAAI,CAACX,GAAG,CAACmC,GAAG,CAAC,WAAW,EAAE;QAC/C3B,eAAe,EAAE;MACnB,CAAC,CAAC;MACF,OAAOG,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOE,KAAU,EAAE;MACnBZ,OAAO,CAACY,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM8C,mBAAmBA,CAACC,EAAU,EAAErC,MAAc,EAAgB;IAClE,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAM,IAAI,CAACX,GAAG,CAACsC,GAAG,CAAC,aAAawB,EAAE,EAAE,EAAE;QAAErC;MAAO,CAAC,EAAE;QACjEjB,eAAe,EAAE;MACnB,CAAC,CAAC;MACF,OAAOG,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOE,KAAU,EAAE;MACnBZ,OAAO,CAACY,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMgD,aAAaA,CAACD,EAAU,EAAgB;IAC5C,IAAI;MACF,MAAMnD,QAAQ,GAAG,MAAM,IAAI,CAACX,GAAG,CAACuC,MAAM,CAAC,aAAauB,EAAE,EAAE,EAAE;QACxDtD,eAAe,EAAE;MACnB,CAAC,CAAC;MACF,OAAOG,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOE,KAAU,EAAE;MACnBZ,OAAO,CAACY,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAMiD,cAAcA,CAACF,EAAU,EAAEG,KAAa,EAAgB;IAC5D,IAAI;MACF,MAAMtD,QAAQ,GAAG,MAAM,IAAI,CAACX,GAAG,CAACqC,IAAI,CAAC,aAAayB,EAAE,QAAQ,EAAE;QAAEG;MAAM,CAAC,EAAE;QACvEzD,eAAe,EAAE;MACnB,CAAC,CAAC;MACF,OAAOG,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOE,KAAU,EAAE;MACnBZ,OAAO,CAACY,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,MAAMA,KAAK;IACb;EACF;AACF;;AAEA;AACA,MAAMmD,gBAAgB,GAAG,IAAIpE,gBAAgB,CAAC,CAAC;;AAE/C;AACA,OAAO,MAAMqE,WAAW,GAAG;EACzBP,WAAW,EAAEA,CAAA,KAAMM,gBAAgB,CAACN,WAAW,CAAC,CAAC;EACjDC,mBAAmB,EAAEA,CAACC,EAAU,EAAErC,MAAc,KAAKyC,gBAAgB,CAACL,mBAAmB,CAACC,EAAE,EAAErC,MAAM,CAAC;EACrGsC,aAAa,EAAGD,EAAU,IAAKI,gBAAgB,CAACH,aAAa,CAACD,EAAE,CAAC;EACjEE,cAAc,EAAEA,CAACF,EAAU,EAAEG,KAAa,KAAKC,gBAAgB,CAACF,cAAc,CAACF,EAAE,EAAEG,KAAK;AAC1F,CAAC;AAED,eAAeC,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}