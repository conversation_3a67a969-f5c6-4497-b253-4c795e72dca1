{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/NewsletterManager.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Pagination, Input, Button, Spin, message, Alert, Modal, Form, Radio, Checkbox, Space, Tooltip } from 'antd';\nimport { SearchOutlined, DownloadOutlined, PlusOutlined, UploadOutlined, FileExcelOutlined, FileTextOutlined, InfoCircleOutlined } from '@ant-design/icons';\nimport { CSVLink } from 'react-csv';\nimport { newsletterAPI } from '../../services/api';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Search\n} = Input;\nconst {\n  TextArea\n} = Input;\nconst NewsletterManager = () => {\n  _s();\n  const [subscribers, setSubscribers] = useState([]);\n  const [filteredSubscribers, setFilteredSubscribers] = useState([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pageSize, setPageSize] = useState(10);\n  const [totalItems, setTotalItems] = useState(0);\n  const [error, setError] = useState(null);\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [isBulkImportModalVisible, setIsBulkImportModalVisible] = useState(false);\n  const [isExportModalVisible, setIsExportModalVisible] = useState(false);\n  const [bulkEmails, setBulkEmails] = useState('');\n  const [exportFormat, setExportFormat] = useState('csv');\n  const [exportFields, setExportFields] = useState(['email', 'createdAt']);\n  const [exportFileName, setExportFileName] = useState('newsletter_subscribers');\n  const [form] = Form.useForm();\n\n  // Use the newsletterAPI directly instead of useAdminApi\n\n  useEffect(() => {\n    fetchSubscribers();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  useEffect(() => {\n    applyFilters();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [subscribers, searchTerm]);\n  const fetchSubscribers = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      try {\n        // Fetch subscribers from API\n        const data = await newsletterAPI.getSubscribers();\n        if (Array.isArray(data)) {\n          setSubscribers(data);\n          setTotalItems(data.length);\n          console.log('Subscribers fetched from API:', data);\n        } else {\n          throw new Error('No data received from API');\n        }\n      } catch (apiError) {\n        console.error('Error fetching subscribers from API:', apiError);\n\n        // Check if it's an authentication error\n        if (apiError.response && apiError.response.status === 401) {\n          setError('Authentication error. Please log in again to view subscribers.');\n          return;\n        }\n\n        // For development or when API fails, use mock data\n        if (process.env.NODE_ENV === 'development' || apiError) {\n          // Mock data for subscribers\n          const mockSubscribers = [{\n            id: 1,\n            email: '<EMAIL>',\n            createdAt: '2023-10-15T08:30:00Z'\n          }, {\n            id: 2,\n            email: '<EMAIL>',\n            createdAt: '2023-10-18T14:45:00Z'\n          }, {\n            id: 3,\n            email: '<EMAIL>',\n            createdAt: '2023-10-20T11:20:00Z'\n          }, {\n            id: 4,\n            email: '<EMAIL>',\n            createdAt: '2023-10-22T09:15:00Z'\n          }, {\n            id: 5,\n            email: '<EMAIL>',\n            createdAt: '2023-10-25T16:30:00Z'\n          }];\n          setSubscribers(mockSubscribers);\n          setTotalItems(mockSubscribers.length);\n          console.log('Using mock subscriber data');\n        }\n      }\n    } catch (error) {\n      console.error('Error in fetchSubscribers:', error);\n      setError('Failed to load subscribers. Please try again later.');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const applyFilters = () => {\n    if (searchTerm) {\n      const filtered = subscribers.filter(subscriber => subscriber.email.toLowerCase().includes(searchTerm.toLowerCase()));\n      setFilteredSubscribers(filtered);\n      setTotalItems(filtered.length);\n    } else {\n      setFilteredSubscribers(subscribers);\n      setTotalItems(subscribers.length);\n    }\n  };\n  const handleSearch = value => {\n    setSearchTerm(value);\n    setCurrentPage(1);\n  };\n  const handlePageChange = (page, pageSize) => {\n    setCurrentPage(page);\n    if (pageSize) setPageSize(pageSize);\n  };\n  const handleDelete = async id => {\n    Modal.confirm({\n      title: 'Remove Subscriber',\n      content: 'Are you sure you want to remove this subscriber?',\n      okText: 'Yes, Remove',\n      okType: 'danger',\n      cancelText: 'Cancel',\n      onOk: async () => {\n        try {\n          console.log('Removing subscriber with ID:', id);\n\n          // Call API to delete subscriber\n          await newsletterAPI.removeSubscriber(id);\n\n          // Update local state\n          const updatedSubscribers = subscribers.filter(subscriber => subscriber.id !== id);\n          setSubscribers(updatedSubscribers);\n          message.success('Subscriber removed successfully');\n        } catch (error) {\n          console.error('Error deleting subscriber:', error);\n          message.error('Failed to remove subscriber');\n        }\n      }\n    });\n  };\n\n  // Add a new subscriber\n  const handleAddSubscriber = async values => {\n    try {\n      // Call API to add subscriber\n      await newsletterAPI.addSubscriber(values.email);\n\n      // Refresh the list\n      fetchSubscribers();\n\n      // Reset form and close modal\n      form.resetFields();\n      setIsModalVisible(false);\n      message.success('Subscriber added successfully');\n    } catch (error) {\n      console.error('Error adding subscriber:', error);\n      message.error('Failed to add subscriber');\n    }\n  };\n\n  // Handle bulk import\n  const handleBulkImport = async () => {\n    try {\n      // Split emails by newline, comma, or semicolon\n      const emails = bulkEmails.split(/[\\n,;]/).map(email => email.trim()).filter(email => email && /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email));\n      if (emails.length === 0) {\n        message.error('No valid emails found');\n        return;\n      }\n\n      // Show loading message\n      const loadingMessage = message.loading(`Importing ${emails.length} subscribers...`, 0);\n      try {\n        // Use the bulk import endpoint\n        const response = await newsletterAPI.bulkImport(emails);\n\n        // Close loading message\n        loadingMessage();\n\n        // Show results\n        if (response.results.success > 0) {\n          message.success(`Successfully imported ${response.results.success} subscribers`);\n        }\n        if (response.results.duplicates > 0) {\n          message.warning(`Skipped ${response.results.duplicates} duplicate subscribers`);\n        }\n        if (response.results.failures > 0) {\n          message.error(`Failed to import ${response.results.failures} subscribers`);\n        }\n      } catch (error) {\n        // Close loading message\n        loadingMessage();\n        console.error('Error calling bulk import API:', error);\n        message.error('Failed to process bulk import');\n      }\n\n      // Refresh the list\n      fetchSubscribers();\n\n      // Reset form and close modal\n      setBulkEmails('');\n      setIsBulkImportModalVisible(false);\n    } catch (error) {\n      console.error('Error in bulk import:', error);\n      message.error('Failed to process bulk import');\n    }\n  };\n\n  // Prepare data for export\n  const prepareExportData = () => {\n    // Filter subscribers based on current search/filter\n    const dataToExport = searchTerm ? filteredSubscribers : subscribers;\n\n    // Format data based on selected fields\n    return dataToExport.map(subscriber => {\n      const formattedData = {};\n      if (exportFields.includes('id')) {\n        formattedData.id = subscriber.id;\n      }\n      if (exportFields.includes('email')) {\n        formattedData.email = subscriber.email;\n      }\n      if (exportFields.includes('createdAt')) {\n        formattedData.subscribed_date = new Date(subscriber.createdAt).toLocaleDateString();\n      }\n      return formattedData;\n    });\n  };\n\n  // Handle export\n  const handleExport = () => {\n    try {\n      const exportData = prepareExportData();\n      if (exportFormat === 'json') {\n        // For JSON export, create and download a JSON file\n        const jsonData = JSON.stringify(exportData, null, 2);\n        const blob = new Blob([jsonData], {\n          type: 'application/json'\n        });\n        const url = URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = `${exportFileName}.json`;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        URL.revokeObjectURL(url);\n        message.success('Subscribers exported successfully as JSON');\n      } else {\n        // CSV export is handled by CSVLink component\n        // Just close the modal\n        message.success('Subscribers exported successfully as CSV');\n      }\n      setIsExportModalVisible(false);\n    } catch (error) {\n      console.error('Error exporting subscribers:', error);\n      message.error('Failed to export subscribers');\n    }\n  };\n\n  // Get CSV headers based on selected fields\n  const getCSVHeaders = () => {\n    const headers = [];\n    if (exportFields.includes('id')) {\n      headers.push({\n        label: 'ID',\n        key: 'id'\n      });\n    }\n    if (exportFields.includes('email')) {\n      headers.push({\n        label: 'Email',\n        key: 'email'\n      });\n    }\n    if (exportFields.includes('createdAt')) {\n      headers.push({\n        label: 'Subscription Date',\n        key: 'subscribed_date'\n      });\n    }\n    return headers;\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-2xl font-bold text-gray-800\",\n        children: \"Newsletter Subscribers\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 19\n          }, this),\n          onClick: () => setIsModalVisible(true),\n          children: \"Add Subscriber\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"default\",\n          icon: /*#__PURE__*/_jsxDEV(UploadOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 19\n          }, this),\n          onClick: () => setIsBulkImportModalVisible(true),\n          children: \"Bulk Import\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 329,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"default\",\n          icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 19\n          }, this),\n          onClick: () => setIsExportModalVisible(true),\n          children: \"Export Data\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 319,\n      columnNumber: 7\n    }, this), error && /*#__PURE__*/_jsxDEV(Alert, {\n      message: \"Error\",\n      description: error,\n      type: \"error\",\n      showIcon: true,\n      closable: true,\n      onClose: () => setError(null),\n      className: \"mb-4\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 348,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white p-4 rounded-lg shadow-md mb-6\",\n      children: /*#__PURE__*/_jsxDEV(Search, {\n        placeholder: \"Search subscribers by email...\",\n        allowClear: true,\n        enterButton: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 24\n        }, this),\n        size: \"large\",\n        onSearch: handleSearch,\n        className: \"w-full\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 360,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow overflow-hidden\",\n      children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex justify-center items-center p-8\",\n        children: /*#__PURE__*/_jsxDEV(Spin, {\n          size: \"large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 375,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 374,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"min-w-full divide-y divide-gray-200\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            className: \"bg-gray-50\",\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Email\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 382,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Subscribed Since\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 383,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n                children: \"Actions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 384,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            className: \"bg-white divide-y divide-gray-200\",\n            children: filteredSubscribers.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n              children: /*#__PURE__*/_jsxDEV(\"td\", {\n                colSpan: 3,\n                className: \"px-6 py-4 text-center text-gray-500\",\n                children: \"No subscribers found\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 19\n            }, this) : filteredSubscribers.slice((currentPage - 1) * pageSize, currentPage * pageSize).map(subscriber => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"hover:bg-gray-50 transition-colors\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-sm font-medium text-gray-900\",\n                  children: subscriber.email\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n                children: new Date(subscriber.createdAt).toLocaleDateString()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 25\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-6 py-4 whitespace-nowrap text-sm font-medium\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  danger: true,\n                  onClick: () => handleDelete(subscriber.id),\n                  className: \"text-red-600 hover:text-red-900\",\n                  children: \"Remove\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 406,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 25\n              }, this)]\n            }, subscriber.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 23\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"px-6 py-4 flex justify-end\",\n          children: /*#__PURE__*/_jsxDEV(Pagination, {\n            current: currentPage,\n            total: totalItems,\n            pageSize: pageSize,\n            onChange: handlePageChange,\n            showSizeChanger: true,\n            showTotal: total => `Total ${total} subscribers`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 422,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 372,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"Add New Subscriber\",\n      open: isModalVisible,\n      onCancel: () => {\n        setIsModalVisible(false);\n        form.resetFields();\n      },\n      footer: null,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleAddSubscriber,\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"email\",\n          label: \"Email Address\",\n          rules: [{\n            required: true,\n            message: 'Please enter an email address'\n          }, {\n            type: 'email',\n            message: 'Please enter a valid email address'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"<EMAIL>\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 459,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          className: \"mb-0 flex justify-end\",\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            type: \"default\",\n            className: \"mr-2\",\n            onClick: () => {\n              setIsModalVisible(false);\n              form.resetFields();\n            },\n            children: \"Cancel\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            htmlType: \"submit\",\n            children: \"Add Subscriber\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 461,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 446,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 437,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"Bulk Import Subscribers\",\n      open: isBulkImportModalVisible,\n      onCancel: () => {\n        setIsBulkImportModalVisible(false);\n        setBulkEmails('');\n      },\n      onOk: handleBulkImport,\n      okText: \"Import\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600\",\n          children: \"Enter email addresses separated by commas, semicolons, or new lines.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextArea, {\n          rows: 10,\n          value: bulkEmails,\n          onChange: e => setBulkEmails(e.target.value),\n          placeholder: \"<EMAIL>\\<EMAIL>\\<EMAIL>\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 490,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 486,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 476,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"Export Subscribers\",\n      open: isExportModalVisible,\n      onCancel: () => setIsExportModalVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setIsExportModalVisible(false),\n        children: \"Cancel\"\n      }, \"cancel\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 505,\n        columnNumber: 11\n      }, this), exportFormat === 'csv' ? /*#__PURE__*/_jsxDEV(CSVLink, {\n        data: prepareExportData(),\n        headers: getCSVHeaders(),\n        filename: `${exportFileName}.csv`,\n        className: \"ant-btn ant-btn-primary\",\n        onClick: () => {\n          message.success('Subscribers exported successfully as CSV');\n          setIsExportModalVisible(false);\n        },\n        children: \"Export CSV\"\n      }, \"export\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 509,\n        columnNumber: 13\n      }, this) : /*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        onClick: handleExport,\n        children: \"Export JSON\"\n      }, \"export\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 523,\n        columnNumber: 13\n      }, this)],\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"mb-2 font-medium\",\n            children: \"Export Format\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 531,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Radio.Group, {\n            value: exportFormat,\n            onChange: e => setExportFormat(e.target.value),\n            className: \"mb-4\",\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              direction: \"vertical\",\n              children: [/*#__PURE__*/_jsxDEV(Radio, {\n                value: \"csv\",\n                children: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(FileExcelOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 540,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"CSV (Comma Separated Values)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 541,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Compatible with Excel, Google Sheets, and most data processing tools\",\n                    children: /*#__PURE__*/_jsxDEV(InfoCircleOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 543,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 542,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 539,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 538,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Radio, {\n                value: \"json\",\n                children: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(FileTextOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 549,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: \"JSON (JavaScript Object Notation)\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 550,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Best for developers or data integration\",\n                    children: /*#__PURE__*/_jsxDEV(InfoCircleOutlined, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 552,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 551,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 548,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 547,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 537,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"mb-2 font-medium\",\n            children: \"Fields to Export\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 561,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Checkbox.Group, {\n            value: exportFields,\n            onChange: values => setExportFields(values),\n            className: \"mb-4\",\n            children: /*#__PURE__*/_jsxDEV(Space, {\n              direction: \"vertical\",\n              children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n                value: \"id\",\n                children: \"ID\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Checkbox, {\n                value: \"email\",\n                children: \"Email Address\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Checkbox, {\n                value: \"createdAt\",\n                children: \"Subscription Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 570,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 562,\n            columnNumber: 13\n          }, this), exportFields.length === 0 && /*#__PURE__*/_jsxDEV(Alert, {\n            message: \"Please select at least one field to export\",\n            type: \"warning\",\n            showIcon: true,\n            className: \"mt-2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 574,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 560,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"mb-2 font-medium\",\n            children: \"File Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 584,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Input, {\n            value: exportFileName,\n            onChange: e => setExportFileName(e.target.value),\n            placeholder: \"newsletter_subscribers\",\n            addonAfter: `.${exportFormat}`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 583,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"Export Information\",\n          description: `This will export ${searchTerm ? filteredSubscribers.length : subscribers.length} subscribers in ${exportFormat.toUpperCase()} format.`,\n          type: \"info\",\n          showIcon: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 593,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 529,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 500,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 318,\n    columnNumber: 5\n  }, this);\n};\n_s(NewsletterManager, \"qefRhzi9IN6SY/2YxvhGJfbcmz4=\", false, function () {\n  return [Form.useForm];\n});\n_c = NewsletterManager;\nexport default NewsletterManager;\nvar _c;\n$RefreshReg$(_c, \"NewsletterManager\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Pagination", "Input", "<PERSON><PERSON>", "Spin", "message", "<PERSON><PERSON>", "Modal", "Form", "Radio", "Checkbox", "Space", "<PERSON><PERSON><PERSON>", "SearchOutlined", "DownloadOutlined", "PlusOutlined", "UploadOutlined", "FileExcelOutlined", "FileTextOutlined", "InfoCircleOutlined", "CSVLink", "newsletterAPI", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Search", "TextArea", "NewsletterManager", "_s", "subscribers", "setSubscribers", "filteredSubscribers", "setFilteredSubscribers", "searchTerm", "setSearchTerm", "loading", "setLoading", "currentPage", "setCurrentPage", "pageSize", "setPageSize", "totalItems", "setTotalItems", "error", "setError", "isModalVisible", "setIsModalVisible", "isBulkImportModalVisible", "setIsBulkImportModalVisible", "isExportModalVisible", "setIsExportModalVisible", "bulkEmails", "setBulkEmails", "exportFormat", "setExportFormat", "exportFields", "setExportFields", "exportFileName", "setExportFileName", "form", "useForm", "fetchSubscribers", "applyFilters", "data", "getSubscribers", "Array", "isArray", "length", "console", "log", "Error", "apiError", "response", "status", "process", "env", "NODE_ENV", "mockSubscribers", "id", "email", "createdAt", "filtered", "filter", "subscriber", "toLowerCase", "includes", "handleSearch", "value", "handlePageChange", "page", "handleDelete", "confirm", "title", "content", "okText", "okType", "cancelText", "onOk", "removeSubscriber", "updatedSubscribers", "success", "handleAddSubscriber", "values", "addSubscriber", "resetFields", "handleBulkImport", "emails", "split", "map", "trim", "test", "loadingMessage", "bulkImport", "results", "duplicates", "warning", "failures", "prepareExportData", "dataToExport", "formattedData", "subscribed_date", "Date", "toLocaleDateString", "handleExport", "exportData", "jsonData", "JSON", "stringify", "blob", "Blob", "type", "url", "URL", "createObjectURL", "link", "document", "createElement", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "getCSVHeaders", "headers", "push", "label", "key", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "icon", "onClick", "description", "showIcon", "closable", "onClose", "placeholder", "allowClear", "enterButton", "size", "onSearch", "colSpan", "slice", "danger", "current", "total", "onChange", "showSizeChanger", "showTotal", "open", "onCancel", "footer", "layout", "onFinish", "<PERSON><PERSON>", "name", "rules", "required", "htmlType", "rows", "e", "target", "filename", "Group", "direction", "addonAfter", "toUpperCase", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/NewsletterManager.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Pagination, Input, Button, Spin, message, Alert, Modal, Form, Radio, Checkbox, Space, Tooltip, Select } from 'antd';\nimport { SearchOutlined, DownloadOutlined, PlusOutlined, UploadOutlined, FileExcelOutlined, FileTextOutlined, FilePdfOutlined, InfoCircleOutlined } from '@ant-design/icons';\n\nimport { CSVLink } from 'react-csv';\nimport { newsletterAPI } from '../../services/api';\n\ninterface Subscriber {\n  id: number;\n  email: string;\n  createdAt: string;\n}\n\nconst { Search } = Input;\nconst { TextArea } = Input;\n\nconst NewsletterManager: React.FC = () => {\n  const [subscribers, setSubscribers] = useState<Subscriber[]>([]);\n  const [filteredSubscribers, setFilteredSubscribers] = useState<Subscriber[]>([]);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [loading, setLoading] = useState(false);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pageSize, setPageSize] = useState(10);\n  const [totalItems, setTotalItems] = useState(0);\n  const [error, setError] = useState<string | null>(null);\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [isBulkImportModalVisible, setIsBulkImportModalVisible] = useState(false);\n  const [isExportModalVisible, setIsExportModalVisible] = useState(false);\n  const [bulkEmails, setBulkEmails] = useState('');\n  const [exportFormat, setExportFormat] = useState<'csv' | 'json'>('csv');\n  const [exportFields, setExportFields] = useState<string[]>(['email', 'createdAt']);\n  const [exportFileName, setExportFileName] = useState('newsletter_subscribers');\n  const [form] = Form.useForm();\n\n  // Use the newsletterAPI directly instead of useAdminApi\n\n  useEffect(() => {\n    fetchSubscribers();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  useEffect(() => {\n    applyFilters();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [subscribers, searchTerm]);\n\n  const fetchSubscribers = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      try {\n        // Fetch subscribers from API\n        const data = await newsletterAPI.getSubscribers();\n\n        if (Array.isArray(data)) {\n          setSubscribers(data);\n          setTotalItems(data.length);\n          console.log('Subscribers fetched from API:', data);\n        } else {\n          throw new Error('No data received from API');\n        }\n      } catch (apiError: any) {\n        console.error('Error fetching subscribers from API:', apiError);\n\n        // Check if it's an authentication error\n        if (apiError.response && apiError.response.status === 401) {\n          setError('Authentication error. Please log in again to view subscribers.');\n          return;\n        }\n\n        // For development or when API fails, use mock data\n        if (process.env.NODE_ENV === 'development' || apiError) {\n          // Mock data for subscribers\n          const mockSubscribers: Subscriber[] = [\n            {\n              id: 1,\n              email: '<EMAIL>',\n              createdAt: '2023-10-15T08:30:00Z'\n            },\n            {\n              id: 2,\n              email: '<EMAIL>',\n              createdAt: '2023-10-18T14:45:00Z'\n            },\n            {\n              id: 3,\n              email: '<EMAIL>',\n              createdAt: '2023-10-20T11:20:00Z'\n            },\n            {\n              id: 4,\n              email: '<EMAIL>',\n              createdAt: '2023-10-22T09:15:00Z'\n            },\n            {\n              id: 5,\n              email: '<EMAIL>',\n              createdAt: '2023-10-25T16:30:00Z'\n            }\n          ];\n\n          setSubscribers(mockSubscribers);\n          setTotalItems(mockSubscribers.length);\n          console.log('Using mock subscriber data');\n        }\n      }\n    } catch (error) {\n      console.error('Error in fetchSubscribers:', error);\n      setError('Failed to load subscribers. Please try again later.');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const applyFilters = () => {\n    if (searchTerm) {\n      const filtered = subscribers.filter(subscriber =>\n        subscriber.email.toLowerCase().includes(searchTerm.toLowerCase())\n      );\n      setFilteredSubscribers(filtered);\n      setTotalItems(filtered.length);\n    } else {\n      setFilteredSubscribers(subscribers);\n      setTotalItems(subscribers.length);\n    }\n  };\n\n  const handleSearch = (value: string) => {\n    setSearchTerm(value);\n    setCurrentPage(1);\n  };\n\n  const handlePageChange = (page: number, pageSize?: number) => {\n    setCurrentPage(page);\n    if (pageSize) setPageSize(pageSize);\n  };\n\n  const handleDelete = async (id: number) => {\n    Modal.confirm({\n      title: 'Remove Subscriber',\n      content: 'Are you sure you want to remove this subscriber?',\n      okText: 'Yes, Remove',\n      okType: 'danger',\n      cancelText: 'Cancel',\n      onOk: async () => {\n        try {\n          console.log('Removing subscriber with ID:', id);\n\n          // Call API to delete subscriber\n          await newsletterAPI.removeSubscriber(id);\n\n          // Update local state\n          const updatedSubscribers = subscribers.filter(subscriber => subscriber.id !== id);\n          setSubscribers(updatedSubscribers);\n\n          message.success('Subscriber removed successfully');\n        } catch (error) {\n          console.error('Error deleting subscriber:', error);\n          message.error('Failed to remove subscriber');\n        }\n      }\n    });\n  };\n\n  // Add a new subscriber\n  const handleAddSubscriber = async (values: { email: string }) => {\n    try {\n      // Call API to add subscriber\n      await newsletterAPI.addSubscriber(values.email);\n\n      // Refresh the list\n      fetchSubscribers();\n\n      // Reset form and close modal\n      form.resetFields();\n      setIsModalVisible(false);\n\n      message.success('Subscriber added successfully');\n    } catch (error) {\n      console.error('Error adding subscriber:', error);\n      message.error('Failed to add subscriber');\n    }\n  };\n\n  // Handle bulk import\n  const handleBulkImport = async () => {\n    try {\n      // Split emails by newline, comma, or semicolon\n      const emails = bulkEmails\n        .split(/[\\n,;]/)\n        .map(email => email.trim())\n        .filter(email => email && /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(email));\n\n      if (emails.length === 0) {\n        message.error('No valid emails found');\n        return;\n      }\n\n      // Show loading message\n      const loadingMessage = message.loading(`Importing ${emails.length} subscribers...`, 0);\n\n      try {\n        // Use the bulk import endpoint\n        const response = await newsletterAPI.bulkImport(emails);\n\n        // Close loading message\n        loadingMessage();\n\n        // Show results\n        if (response.results.success > 0) {\n          message.success(`Successfully imported ${response.results.success} subscribers`);\n        }\n\n        if (response.results.duplicates > 0) {\n          message.warning(`Skipped ${response.results.duplicates} duplicate subscribers`);\n        }\n\n        if (response.results.failures > 0) {\n          message.error(`Failed to import ${response.results.failures} subscribers`);\n        }\n      } catch (error) {\n        // Close loading message\n        loadingMessage();\n        console.error('Error calling bulk import API:', error);\n        message.error('Failed to process bulk import');\n      }\n\n      // Refresh the list\n      fetchSubscribers();\n\n      // Reset form and close modal\n      setBulkEmails('');\n      setIsBulkImportModalVisible(false);\n    } catch (error) {\n      console.error('Error in bulk import:', error);\n      message.error('Failed to process bulk import');\n    }\n  };\n\n  // Prepare data for export\n  const prepareExportData = () => {\n    // Filter subscribers based on current search/filter\n    const dataToExport = searchTerm ? filteredSubscribers : subscribers;\n\n    // Format data based on selected fields\n    return dataToExport.map(subscriber => {\n      const formattedData: Record<string, any> = {};\n\n      if (exportFields.includes('id')) {\n        formattedData.id = subscriber.id;\n      }\n\n      if (exportFields.includes('email')) {\n        formattedData.email = subscriber.email;\n      }\n\n      if (exportFields.includes('createdAt')) {\n        formattedData.subscribed_date = new Date(subscriber.createdAt).toLocaleDateString();\n      }\n\n      return formattedData;\n    });\n  };\n\n  // Handle export\n  const handleExport = () => {\n    try {\n      const exportData = prepareExportData();\n\n      if (exportFormat === 'json') {\n        // For JSON export, create and download a JSON file\n        const jsonData = JSON.stringify(exportData, null, 2);\n        const blob = new Blob([jsonData], { type: 'application/json' });\n        const url = URL.createObjectURL(blob);\n        const link = document.createElement('a');\n        link.href = url;\n        link.download = `${exportFileName}.json`;\n        document.body.appendChild(link);\n        link.click();\n        document.body.removeChild(link);\n        URL.revokeObjectURL(url);\n\n        message.success('Subscribers exported successfully as JSON');\n      } else {\n        // CSV export is handled by CSVLink component\n        // Just close the modal\n        message.success('Subscribers exported successfully as CSV');\n      }\n\n      setIsExportModalVisible(false);\n    } catch (error) {\n      console.error('Error exporting subscribers:', error);\n      message.error('Failed to export subscribers');\n    }\n  };\n\n  // Get CSV headers based on selected fields\n  const getCSVHeaders = () => {\n    const headers = [];\n\n    if (exportFields.includes('id')) {\n      headers.push({ label: 'ID', key: 'id' });\n    }\n\n    if (exportFields.includes('email')) {\n      headers.push({ label: 'Email', key: 'email' });\n    }\n\n    if (exportFields.includes('createdAt')) {\n      headers.push({ label: 'Subscription Date', key: 'subscribed_date' });\n    }\n\n    return headers;\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <h2 className=\"text-2xl font-bold text-gray-800\">Newsletter Subscribers</h2>\n        <div className=\"space-x-2\">\n          <Button\n            type=\"primary\"\n            icon={<PlusOutlined />}\n            onClick={() => setIsModalVisible(true)}\n          >\n            Add Subscriber\n          </Button>\n          <Button\n            type=\"default\"\n            icon={<UploadOutlined />}\n            onClick={() => setIsBulkImportModalVisible(true)}\n          >\n            Bulk Import\n          </Button>\n          <Button\n            type=\"default\"\n            icon={<DownloadOutlined />}\n            onClick={() => setIsExportModalVisible(true)}\n          >\n            Export Data\n          </Button>\n        </div>\n      </div>\n\n      {/* Error Alert */}\n      {error && (\n        <Alert\n          message=\"Error\"\n          description={error}\n          type=\"error\"\n          showIcon\n          closable\n          onClose={() => setError(null)}\n          className=\"mb-4\"\n        />\n      )}\n\n      {/* Search */}\n      <div className=\"bg-white p-4 rounded-lg shadow-md mb-6\">\n        <Search\n          placeholder=\"Search subscribers by email...\"\n          allowClear\n          enterButton={<SearchOutlined />}\n          size=\"large\"\n          onSearch={handleSearch}\n          className=\"w-full\"\n        />\n      </div>\n\n      {/* Subscribers Table */}\n      <div className=\"bg-white rounded-lg shadow overflow-hidden\">\n        {loading ? (\n          <div className=\"flex justify-center items-center p-8\">\n            <Spin size=\"large\" />\n          </div>\n        ) : (\n          <>\n            <table className=\"min-w-full divide-y divide-gray-200\">\n              <thead className=\"bg-gray-50\">\n                <tr>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Email</th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Subscribed Since</th>\n                  <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">Actions</th>\n                </tr>\n              </thead>\n              <tbody className=\"bg-white divide-y divide-gray-200\">\n                {filteredSubscribers.length === 0 ? (\n                  <tr>\n                    <td colSpan={3} className=\"px-6 py-4 text-center text-gray-500\">\n                      No subscribers found\n                    </td>\n                  </tr>\n                ) : (\n                  filteredSubscribers\n                    .slice((currentPage - 1) * pageSize, currentPage * pageSize)\n                    .map((subscriber) => (\n                      <tr key={subscriber.id} className=\"hover:bg-gray-50 transition-colors\">\n                        <td className=\"px-6 py-4 whitespace-nowrap\">\n                          <div className=\"text-sm font-medium text-gray-900\">{subscriber.email}</div>\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                          {new Date(subscriber.createdAt).toLocaleDateString()}\n                        </td>\n                        <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium\">\n                          <Button\n                            type=\"link\"\n                            danger\n                            onClick={() => handleDelete(subscriber.id)}\n                            className=\"text-red-600 hover:text-red-900\"\n                          >\n                            Remove\n                          </Button>\n                        </td>\n                      </tr>\n                    ))\n                )}\n              </tbody>\n            </table>\n\n            {/* Pagination */}\n            <div className=\"px-6 py-4 flex justify-end\">\n              <Pagination\n                current={currentPage}\n                total={totalItems}\n                pageSize={pageSize}\n                onChange={handlePageChange}\n                showSizeChanger\n                showTotal={(total) => `Total ${total} subscribers`}\n              />\n            </div>\n          </>\n        )}\n      </div>\n\n      {/* Add Subscriber Modal */}\n      <Modal\n        title=\"Add New Subscriber\"\n        open={isModalVisible}\n        onCancel={() => {\n          setIsModalVisible(false);\n          form.resetFields();\n        }}\n        footer={null}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleAddSubscriber}\n        >\n          <Form.Item\n            name=\"email\"\n            label=\"Email Address\"\n            rules={[\n              { required: true, message: 'Please enter an email address' },\n              { type: 'email', message: 'Please enter a valid email address' }\n            ]}\n          >\n            <Input placeholder=\"<EMAIL>\" />\n          </Form.Item>\n          <Form.Item className=\"mb-0 flex justify-end\">\n            <Button type=\"default\" className=\"mr-2\" onClick={() => {\n              setIsModalVisible(false);\n              form.resetFields();\n            }}>\n              Cancel\n            </Button>\n            <Button type=\"primary\" htmlType=\"submit\">\n              Add Subscriber\n            </Button>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* Bulk Import Modal */}\n      <Modal\n        title=\"Bulk Import Subscribers\"\n        open={isBulkImportModalVisible}\n        onCancel={() => {\n          setIsBulkImportModalVisible(false);\n          setBulkEmails('');\n        }}\n        onOk={handleBulkImport}\n        okText=\"Import\"\n      >\n        <div className=\"space-y-4\">\n          <p className=\"text-gray-600\">\n            Enter email addresses separated by commas, semicolons, or new lines.\n          </p>\n          <TextArea\n            rows={10}\n            value={bulkEmails}\n            onChange={(e) => setBulkEmails(e.target.value)}\n            placeholder=\"<EMAIL>&#10;<EMAIL>&#10;<EMAIL>\"\n          />\n        </div>\n      </Modal>\n\n      {/* Export Modal */}\n      <Modal\n        title=\"Export Subscribers\"\n        open={isExportModalVisible}\n        onCancel={() => setIsExportModalVisible(false)}\n        footer={[\n          <Button key=\"cancel\" onClick={() => setIsExportModalVisible(false)}>\n            Cancel\n          </Button>,\n          exportFormat === 'csv' ? (\n            <CSVLink\n              key=\"export\"\n              data={prepareExportData()}\n              headers={getCSVHeaders()}\n              filename={`${exportFileName}.csv`}\n              className=\"ant-btn ant-btn-primary\"\n              onClick={() => {\n                message.success('Subscribers exported successfully as CSV');\n                setIsExportModalVisible(false);\n              }}\n            >\n              Export CSV\n            </CSVLink>\n          ) : (\n            <Button key=\"export\" type=\"primary\" onClick={handleExport}>\n              Export JSON\n            </Button>\n          )\n        ]}\n      >\n        <div className=\"space-y-6\">\n          <div>\n            <h4 className=\"mb-2 font-medium\">Export Format</h4>\n            <Radio.Group\n              value={exportFormat}\n              onChange={(e) => setExportFormat(e.target.value)}\n              className=\"mb-4\"\n            >\n              <Space direction=\"vertical\">\n                <Radio value=\"csv\">\n                  <Space>\n                    <FileExcelOutlined />\n                    <span>CSV (Comma Separated Values)</span>\n                    <Tooltip title=\"Compatible with Excel, Google Sheets, and most data processing tools\">\n                      <InfoCircleOutlined />\n                    </Tooltip>\n                  </Space>\n                </Radio>\n                <Radio value=\"json\">\n                  <Space>\n                    <FileTextOutlined />\n                    <span>JSON (JavaScript Object Notation)</span>\n                    <Tooltip title=\"Best for developers or data integration\">\n                      <InfoCircleOutlined />\n                    </Tooltip>\n                  </Space>\n                </Radio>\n              </Space>\n            </Radio.Group>\n          </div>\n\n          <div>\n            <h4 className=\"mb-2 font-medium\">Fields to Export</h4>\n            <Checkbox.Group\n              value={exportFields}\n              onChange={(values) => setExportFields(values as string[])}\n              className=\"mb-4\"\n            >\n              <Space direction=\"vertical\">\n                <Checkbox value=\"id\">ID</Checkbox>\n                <Checkbox value=\"email\">Email Address</Checkbox>\n                <Checkbox value=\"createdAt\">Subscription Date</Checkbox>\n              </Space>\n            </Checkbox.Group>\n            {exportFields.length === 0 && (\n              <Alert\n                message=\"Please select at least one field to export\"\n                type=\"warning\"\n                showIcon\n                className=\"mt-2\"\n              />\n            )}\n          </div>\n\n          <div>\n            <h4 className=\"mb-2 font-medium\">File Name</h4>\n            <Input\n              value={exportFileName}\n              onChange={(e) => setExportFileName(e.target.value)}\n              placeholder=\"newsletter_subscribers\"\n              addonAfter={`.${exportFormat}`}\n            />\n          </div>\n\n          <Alert\n            message=\"Export Information\"\n            description={`This will export ${searchTerm ? filteredSubscribers.length : subscribers.length} subscribers in ${exportFormat.toUpperCase()} format.`}\n            type=\"info\"\n            showIcon\n          />\n        </div>\n      </Modal>\n    </div>\n  );\n};\n\nexport default NewsletterManager;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,UAAU,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,OAAO,QAAgB,MAAM;AAC5H,SAASC,cAAc,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,gBAAgB,EAAmBC,kBAAkB,QAAQ,mBAAmB;AAE5K,SAASC,OAAO,QAAQ,WAAW;AACnC,SAASC,aAAa,QAAQ,oBAAoB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAQnD,MAAM;EAAEC;AAAO,CAAC,GAAGxB,KAAK;AACxB,MAAM;EAAEyB;AAAS,CAAC,GAAGzB,KAAK;AAE1B,MAAM0B,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGhC,QAAQ,CAAe,EAAE,CAAC;EAChE,MAAM,CAACiC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGlC,QAAQ,CAAe,EAAE,CAAC;EAChF,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuC,WAAW,EAAEC,cAAc,CAAC,GAAGxC,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACyC,QAAQ,EAAEC,WAAW,CAAC,GAAG1C,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC2C,UAAU,EAAEC,aAAa,CAAC,GAAG5C,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAAC6C,KAAK,EAAEC,QAAQ,CAAC,GAAG9C,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC+C,cAAc,EAAEC,iBAAiB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACiD,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGlD,QAAQ,CAAC,KAAK,CAAC;EAC/E,MAAM,CAACmD,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGpD,QAAQ,CAAC,KAAK,CAAC;EACvE,MAAM,CAACqD,UAAU,EAAEC,aAAa,CAAC,GAAGtD,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACuD,YAAY,EAAEC,eAAe,CAAC,GAAGxD,QAAQ,CAAiB,KAAK,CAAC;EACvE,MAAM,CAACyD,YAAY,EAAEC,eAAe,CAAC,GAAG1D,QAAQ,CAAW,CAAC,OAAO,EAAE,WAAW,CAAC,CAAC;EAClF,MAAM,CAAC2D,cAAc,EAAEC,iBAAiB,CAAC,GAAG5D,QAAQ,CAAC,wBAAwB,CAAC;EAC9E,MAAM,CAAC6D,IAAI,CAAC,GAAGpD,IAAI,CAACqD,OAAO,CAAC,CAAC;;EAE7B;;EAEA7D,SAAS,CAAC,MAAM;IACd8D,gBAAgB,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,EAAE,CAAC;EAEN9D,SAAS,CAAC,MAAM;IACd+D,YAAY,CAAC,CAAC;IACd;EACF,CAAC,EAAE,CAACjC,WAAW,EAAEI,UAAU,CAAC,CAAC;EAE7B,MAAM4B,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACFzB,UAAU,CAAC,IAAI,CAAC;MAChBQ,QAAQ,CAAC,IAAI,CAAC;MAEd,IAAI;QACF;QACA,MAAMmB,IAAI,GAAG,MAAM3C,aAAa,CAAC4C,cAAc,CAAC,CAAC;QAEjD,IAAIC,KAAK,CAACC,OAAO,CAACH,IAAI,CAAC,EAAE;UACvBjC,cAAc,CAACiC,IAAI,CAAC;UACpBrB,aAAa,CAACqB,IAAI,CAACI,MAAM,CAAC;UAC1BC,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAEN,IAAI,CAAC;QACpD,CAAC,MAAM;UACL,MAAM,IAAIO,KAAK,CAAC,2BAA2B,CAAC;QAC9C;MACF,CAAC,CAAC,OAAOC,QAAa,EAAE;QACtBH,OAAO,CAACzB,KAAK,CAAC,sCAAsC,EAAE4B,QAAQ,CAAC;;QAE/D;QACA,IAAIA,QAAQ,CAACC,QAAQ,IAAID,QAAQ,CAACC,QAAQ,CAACC,MAAM,KAAK,GAAG,EAAE;UACzD7B,QAAQ,CAAC,gEAAgE,CAAC;UAC1E;QACF;;QAEA;QACA,IAAI8B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAIL,QAAQ,EAAE;UACtD;UACA,MAAMM,eAA6B,GAAG,CACpC;YACEC,EAAE,EAAE,CAAC;YACLC,KAAK,EAAE,sBAAsB;YAC7BC,SAAS,EAAE;UACb,CAAC,EACD;YACEF,EAAE,EAAE,CAAC;YACLC,KAAK,EAAE,wBAAwB;YAC/BC,SAAS,EAAE;UACb,CAAC,EACD;YACEF,EAAE,EAAE,CAAC;YACLC,KAAK,EAAE,4BAA4B;YACnCC,SAAS,EAAE;UACb,CAAC,EACD;YACEF,EAAE,EAAE,CAAC;YACLC,KAAK,EAAE,4BAA4B;YACnCC,SAAS,EAAE;UACb,CAAC,EACD;YACEF,EAAE,EAAE,CAAC;YACLC,KAAK,EAAE,2BAA2B;YAClCC,SAAS,EAAE;UACb,CAAC,CACF;UAEDlD,cAAc,CAAC+C,eAAe,CAAC;UAC/BnC,aAAa,CAACmC,eAAe,CAACV,MAAM,CAAC;UACrCC,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;QAC3C;MACF;IACF,CAAC,CAAC,OAAO1B,KAAK,EAAE;MACdyB,OAAO,CAACzB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClDC,QAAQ,CAAC,qDAAqD,CAAC;IACjE,CAAC,SAAS;MACRR,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM0B,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI7B,UAAU,EAAE;MACd,MAAMgD,QAAQ,GAAGpD,WAAW,CAACqD,MAAM,CAACC,UAAU,IAC5CA,UAAU,CAACJ,KAAK,CAACK,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACpD,UAAU,CAACmD,WAAW,CAAC,CAAC,CAClE,CAAC;MACDpD,sBAAsB,CAACiD,QAAQ,CAAC;MAChCvC,aAAa,CAACuC,QAAQ,CAACd,MAAM,CAAC;IAChC,CAAC,MAAM;MACLnC,sBAAsB,CAACH,WAAW,CAAC;MACnCa,aAAa,CAACb,WAAW,CAACsC,MAAM,CAAC;IACnC;EACF,CAAC;EAED,MAAMmB,YAAY,GAAIC,KAAa,IAAK;IACtCrD,aAAa,CAACqD,KAAK,CAAC;IACpBjD,cAAc,CAAC,CAAC,CAAC;EACnB,CAAC;EAED,MAAMkD,gBAAgB,GAAGA,CAACC,IAAY,EAAElD,QAAiB,KAAK;IAC5DD,cAAc,CAACmD,IAAI,CAAC;IACpB,IAAIlD,QAAQ,EAAEC,WAAW,CAACD,QAAQ,CAAC;EACrC,CAAC;EAED,MAAMmD,YAAY,GAAG,MAAOZ,EAAU,IAAK;IACzCxE,KAAK,CAACqF,OAAO,CAAC;MACZC,KAAK,EAAE,mBAAmB;MAC1BC,OAAO,EAAE,kDAAkD;MAC3DC,MAAM,EAAE,aAAa;MACrBC,MAAM,EAAE,QAAQ;MAChBC,UAAU,EAAE,QAAQ;MACpBC,IAAI,EAAE,MAAAA,CAAA,KAAY;QAChB,IAAI;UACF7B,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAES,EAAE,CAAC;;UAE/C;UACA,MAAM1D,aAAa,CAAC8E,gBAAgB,CAACpB,EAAE,CAAC;;UAExC;UACA,MAAMqB,kBAAkB,GAAGtE,WAAW,CAACqD,MAAM,CAACC,UAAU,IAAIA,UAAU,CAACL,EAAE,KAAKA,EAAE,CAAC;UACjFhD,cAAc,CAACqE,kBAAkB,CAAC;UAElC/F,OAAO,CAACgG,OAAO,CAAC,iCAAiC,CAAC;QACpD,CAAC,CAAC,OAAOzD,KAAK,EAAE;UACdyB,OAAO,CAACzB,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;UAClDvC,OAAO,CAACuC,KAAK,CAAC,6BAA6B,CAAC;QAC9C;MACF;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM0D,mBAAmB,GAAG,MAAOC,MAAyB,IAAK;IAC/D,IAAI;MACF;MACA,MAAMlF,aAAa,CAACmF,aAAa,CAACD,MAAM,CAACvB,KAAK,CAAC;;MAE/C;MACAlB,gBAAgB,CAAC,CAAC;;MAElB;MACAF,IAAI,CAAC6C,WAAW,CAAC,CAAC;MAClB1D,iBAAiB,CAAC,KAAK,CAAC;MAExB1C,OAAO,CAACgG,OAAO,CAAC,+BAA+B,CAAC;IAClD,CAAC,CAAC,OAAOzD,KAAK,EAAE;MACdyB,OAAO,CAACzB,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDvC,OAAO,CAACuC,KAAK,CAAC,0BAA0B,CAAC;IAC3C;EACF,CAAC;;EAED;EACA,MAAM8D,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF;MACA,MAAMC,MAAM,GAAGvD,UAAU,CACtBwD,KAAK,CAAC,QAAQ,CAAC,CACfC,GAAG,CAAC7B,KAAK,IAAIA,KAAK,CAAC8B,IAAI,CAAC,CAAC,CAAC,CAC1B3B,MAAM,CAACH,KAAK,IAAIA,KAAK,IAAI,4BAA4B,CAAC+B,IAAI,CAAC/B,KAAK,CAAC,CAAC;MAErE,IAAI2B,MAAM,CAACvC,MAAM,KAAK,CAAC,EAAE;QACvB/D,OAAO,CAACuC,KAAK,CAAC,uBAAuB,CAAC;QACtC;MACF;;MAEA;MACA,MAAMoE,cAAc,GAAG3G,OAAO,CAAC+B,OAAO,CAAC,aAAauE,MAAM,CAACvC,MAAM,iBAAiB,EAAE,CAAC,CAAC;MAEtF,IAAI;QACF;QACA,MAAMK,QAAQ,GAAG,MAAMpD,aAAa,CAAC4F,UAAU,CAACN,MAAM,CAAC;;QAEvD;QACAK,cAAc,CAAC,CAAC;;QAEhB;QACA,IAAIvC,QAAQ,CAACyC,OAAO,CAACb,OAAO,GAAG,CAAC,EAAE;UAChChG,OAAO,CAACgG,OAAO,CAAC,yBAAyB5B,QAAQ,CAACyC,OAAO,CAACb,OAAO,cAAc,CAAC;QAClF;QAEA,IAAI5B,QAAQ,CAACyC,OAAO,CAACC,UAAU,GAAG,CAAC,EAAE;UACnC9G,OAAO,CAAC+G,OAAO,CAAC,WAAW3C,QAAQ,CAACyC,OAAO,CAACC,UAAU,wBAAwB,CAAC;QACjF;QAEA,IAAI1C,QAAQ,CAACyC,OAAO,CAACG,QAAQ,GAAG,CAAC,EAAE;UACjChH,OAAO,CAACuC,KAAK,CAAC,oBAAoB6B,QAAQ,CAACyC,OAAO,CAACG,QAAQ,cAAc,CAAC;QAC5E;MACF,CAAC,CAAC,OAAOzE,KAAK,EAAE;QACd;QACAoE,cAAc,CAAC,CAAC;QAChB3C,OAAO,CAACzB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtDvC,OAAO,CAACuC,KAAK,CAAC,+BAA+B,CAAC;MAChD;;MAEA;MACAkB,gBAAgB,CAAC,CAAC;;MAElB;MACAT,aAAa,CAAC,EAAE,CAAC;MACjBJ,2BAA2B,CAAC,KAAK,CAAC;IACpC,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdyB,OAAO,CAACzB,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CvC,OAAO,CAACuC,KAAK,CAAC,+BAA+B,CAAC;IAChD;EACF,CAAC;;EAED;EACA,MAAM0E,iBAAiB,GAAGA,CAAA,KAAM;IAC9B;IACA,MAAMC,YAAY,GAAGrF,UAAU,GAAGF,mBAAmB,GAAGF,WAAW;;IAEnE;IACA,OAAOyF,YAAY,CAACV,GAAG,CAACzB,UAAU,IAAI;MACpC,MAAMoC,aAAkC,GAAG,CAAC,CAAC;MAE7C,IAAIhE,YAAY,CAAC8B,QAAQ,CAAC,IAAI,CAAC,EAAE;QAC/BkC,aAAa,CAACzC,EAAE,GAAGK,UAAU,CAACL,EAAE;MAClC;MAEA,IAAIvB,YAAY,CAAC8B,QAAQ,CAAC,OAAO,CAAC,EAAE;QAClCkC,aAAa,CAACxC,KAAK,GAAGI,UAAU,CAACJ,KAAK;MACxC;MAEA,IAAIxB,YAAY,CAAC8B,QAAQ,CAAC,WAAW,CAAC,EAAE;QACtCkC,aAAa,CAACC,eAAe,GAAG,IAAIC,IAAI,CAACtC,UAAU,CAACH,SAAS,CAAC,CAAC0C,kBAAkB,CAAC,CAAC;MACrF;MAEA,OAAOH,aAAa;IACtB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMI,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAI;MACF,MAAMC,UAAU,GAAGP,iBAAiB,CAAC,CAAC;MAEtC,IAAIhE,YAAY,KAAK,MAAM,EAAE;QAC3B;QACA,MAAMwE,QAAQ,GAAGC,IAAI,CAACC,SAAS,CAACH,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;QACpD,MAAMI,IAAI,GAAG,IAAIC,IAAI,CAAC,CAACJ,QAAQ,CAAC,EAAE;UAAEK,IAAI,EAAE;QAAmB,CAAC,CAAC;QAC/D,MAAMC,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACL,IAAI,CAAC;QACrC,MAAMM,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGN,GAAG;QACfG,IAAI,CAACI,QAAQ,GAAG,GAAGjF,cAAc,OAAO;QACxC8E,QAAQ,CAACI,IAAI,CAACC,WAAW,CAACN,IAAI,CAAC;QAC/BA,IAAI,CAACO,KAAK,CAAC,CAAC;QACZN,QAAQ,CAACI,IAAI,CAACG,WAAW,CAACR,IAAI,CAAC;QAC/BF,GAAG,CAACW,eAAe,CAACZ,GAAG,CAAC;QAExB/H,OAAO,CAACgG,OAAO,CAAC,2CAA2C,CAAC;MAC9D,CAAC,MAAM;QACL;QACA;QACAhG,OAAO,CAACgG,OAAO,CAAC,0CAA0C,CAAC;MAC7D;MAEAlD,uBAAuB,CAAC,KAAK,CAAC;IAChC,CAAC,CAAC,OAAOP,KAAK,EAAE;MACdyB,OAAO,CAACzB,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDvC,OAAO,CAACuC,KAAK,CAAC,8BAA8B,CAAC;IAC/C;EACF,CAAC;;EAED;EACA,MAAMqG,aAAa,GAAGA,CAAA,KAAM;IAC1B,MAAMC,OAAO,GAAG,EAAE;IAElB,IAAI1F,YAAY,CAAC8B,QAAQ,CAAC,IAAI,CAAC,EAAE;MAC/B4D,OAAO,CAACC,IAAI,CAAC;QAAEC,KAAK,EAAE,IAAI;QAAEC,GAAG,EAAE;MAAK,CAAC,CAAC;IAC1C;IAEA,IAAI7F,YAAY,CAAC8B,QAAQ,CAAC,OAAO,CAAC,EAAE;MAClC4D,OAAO,CAACC,IAAI,CAAC;QAAEC,KAAK,EAAE,OAAO;QAAEC,GAAG,EAAE;MAAQ,CAAC,CAAC;IAChD;IAEA,IAAI7F,YAAY,CAAC8B,QAAQ,CAAC,WAAW,CAAC,EAAE;MACtC4D,OAAO,CAACC,IAAI,CAAC;QAAEC,KAAK,EAAE,mBAAmB;QAAEC,GAAG,EAAE;MAAkB,CAAC,CAAC;IACtE;IAEA,OAAOH,OAAO;EAChB,CAAC;EAED,oBACE3H,OAAA;IAAK+H,SAAS,EAAC,WAAW;IAAAC,QAAA,gBACxBhI,OAAA;MAAK+H,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChDhI,OAAA;QAAI+H,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAC;MAAsB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC5EpI,OAAA;QAAK+H,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBhI,OAAA,CAACpB,MAAM;UACLgI,IAAI,EAAC,SAAS;UACdyB,IAAI,eAAErI,OAAA,CAACR,YAAY;YAAAyI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBE,OAAO,EAAEA,CAAA,KAAM9G,iBAAiB,CAAC,IAAI,CAAE;UAAAwG,QAAA,EACxC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpI,OAAA,CAACpB,MAAM;UACLgI,IAAI,EAAC,SAAS;UACdyB,IAAI,eAAErI,OAAA,CAACP,cAAc;YAAAwI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBE,OAAO,EAAEA,CAAA,KAAM5G,2BAA2B,CAAC,IAAI,CAAE;UAAAsG,QAAA,EAClD;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTpI,OAAA,CAACpB,MAAM;UACLgI,IAAI,EAAC,SAAS;UACdyB,IAAI,eAAErI,OAAA,CAACT,gBAAgB;YAAA0I,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BE,OAAO,EAAEA,CAAA,KAAM1G,uBAAuB,CAAC,IAAI,CAAE;UAAAoG,QAAA,EAC9C;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,EAGL/G,KAAK,iBACJrB,OAAA,CAACjB,KAAK;MACJD,OAAO,EAAC,OAAO;MACfyJ,WAAW,EAAElH,KAAM;MACnBuF,IAAI,EAAC,OAAO;MACZ4B,QAAQ;MACRC,QAAQ;MACRC,OAAO,EAAEA,CAAA,KAAMpH,QAAQ,CAAC,IAAI,CAAE;MAC9ByG,SAAS,EAAC;IAAM;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjB,CACF,eAGDpI,OAAA;MAAK+H,SAAS,EAAC,wCAAwC;MAAAC,QAAA,eACrDhI,OAAA,CAACG,MAAM;QACLwI,WAAW,EAAC,gCAAgC;QAC5CC,UAAU;QACVC,WAAW,eAAE7I,OAAA,CAACV,cAAc;UAAA2I,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAChCU,IAAI,EAAC,OAAO;QACZC,QAAQ,EAAE/E,YAAa;QACvB+D,SAAS,EAAC;MAAQ;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNpI,OAAA;MAAK+H,SAAS,EAAC,4CAA4C;MAAAC,QAAA,EACxDnH,OAAO,gBACNb,OAAA;QAAK+H,SAAS,EAAC,sCAAsC;QAAAC,QAAA,eACnDhI,OAAA,CAACnB,IAAI;UAACiK,IAAI,EAAC;QAAO;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC,gBAENpI,OAAA,CAAAE,SAAA;QAAA8H,QAAA,gBACEhI,OAAA;UAAO+H,SAAS,EAAC,qCAAqC;UAAAC,QAAA,gBACpDhI,OAAA;YAAO+H,SAAS,EAAC,YAAY;YAAAC,QAAA,eAC3BhI,OAAA;cAAAgI,QAAA,gBACEhI,OAAA;gBAAI+H,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACzGpI,OAAA;gBAAI+H,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAgB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpHpI,OAAA;gBAAI+H,SAAS,EAAC,gFAAgF;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACRpI,OAAA;YAAO+H,SAAS,EAAC,mCAAmC;YAAAC,QAAA,EACjDvH,mBAAmB,CAACoC,MAAM,KAAK,CAAC,gBAC/B7C,OAAA;cAAAgI,QAAA,eACEhI,OAAA;gBAAIgJ,OAAO,EAAE,CAAE;gBAACjB,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAEhE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,GAEL3H,mBAAmB,CAChBwI,KAAK,CAAC,CAAClI,WAAW,GAAG,CAAC,IAAIE,QAAQ,EAAEF,WAAW,GAAGE,QAAQ,CAAC,CAC3DqE,GAAG,CAAEzB,UAAU,iBACd7D,OAAA;cAAwB+H,SAAS,EAAC,oCAAoC;cAAAC,QAAA,gBACpEhI,OAAA;gBAAI+H,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,eACzChI,OAAA;kBAAK+H,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAEnE,UAAU,CAACJ;gBAAK;kBAAAwE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzE,CAAC,eACLpI,OAAA;gBAAI+H,SAAS,EAAC,mDAAmD;gBAAAC,QAAA,EAC9D,IAAI7B,IAAI,CAACtC,UAAU,CAACH,SAAS,CAAC,CAAC0C,kBAAkB,CAAC;cAAC;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,eACLpI,OAAA;gBAAI+H,SAAS,EAAC,iDAAiD;gBAAAC,QAAA,eAC7DhI,OAAA,CAACpB,MAAM;kBACLgI,IAAI,EAAC,MAAM;kBACXsC,MAAM;kBACNZ,OAAO,EAAEA,CAAA,KAAMlE,YAAY,CAACP,UAAU,CAACL,EAAE,CAAE;kBAC3CuE,SAAS,EAAC,iCAAiC;kBAAAC,QAAA,EAC5C;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GAhBEvE,UAAU,CAACL,EAAE;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAiBlB,CACL;UACJ;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGRpI,OAAA;UAAK+H,SAAS,EAAC,4BAA4B;UAAAC,QAAA,eACzChI,OAAA,CAACtB,UAAU;YACTyK,OAAO,EAAEpI,WAAY;YACrBqI,KAAK,EAAEjI,UAAW;YAClBF,QAAQ,EAAEA,QAAS;YACnBoI,QAAQ,EAAEnF,gBAAiB;YAC3BoF,eAAe;YACfC,SAAS,EAAGH,KAAK,IAAK,SAASA,KAAK;UAAe;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA,eACN;IACH;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNpI,OAAA,CAAChB,KAAK;MACJsF,KAAK,EAAC,oBAAoB;MAC1BkF,IAAI,EAAEjI,cAAe;MACrBkI,QAAQ,EAAEA,CAAA,KAAM;QACdjI,iBAAiB,CAAC,KAAK,CAAC;QACxBa,IAAI,CAAC6C,WAAW,CAAC,CAAC;MACpB,CAAE;MACFwE,MAAM,EAAE,IAAK;MAAA1B,QAAA,eAEbhI,OAAA,CAACf,IAAI;QACHoD,IAAI,EAAEA,IAAK;QACXsH,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAE7E,mBAAoB;QAAAiD,QAAA,gBAE9BhI,OAAA,CAACf,IAAI,CAAC4K,IAAI;UACRC,IAAI,EAAC,OAAO;UACZjC,KAAK,EAAC,eAAe;UACrBkC,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAElL,OAAO,EAAE;UAAgC,CAAC,EAC5D;YAAE8H,IAAI,EAAE,OAAO;YAAE9H,OAAO,EAAE;UAAqC,CAAC,CAChE;UAAAkJ,QAAA,eAEFhI,OAAA,CAACrB,KAAK;YAACgK,WAAW,EAAC;UAAwB;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eACZpI,OAAA,CAACf,IAAI,CAAC4K,IAAI;UAAC9B,SAAS,EAAC,uBAAuB;UAAAC,QAAA,gBAC1ChI,OAAA,CAACpB,MAAM;YAACgI,IAAI,EAAC,SAAS;YAACmB,SAAS,EAAC,MAAM;YAACO,OAAO,EAAEA,CAAA,KAAM;cACrD9G,iBAAiB,CAAC,KAAK,CAAC;cACxBa,IAAI,CAAC6C,WAAW,CAAC,CAAC;YACpB,CAAE;YAAA8C,QAAA,EAAC;UAEH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTpI,OAAA,CAACpB,MAAM;YAACgI,IAAI,EAAC,SAAS;YAACqD,QAAQ,EAAC,QAAQ;YAAAjC,QAAA,EAAC;UAEzC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGRpI,OAAA,CAAChB,KAAK;MACJsF,KAAK,EAAC,yBAAyB;MAC/BkF,IAAI,EAAE/H,wBAAyB;MAC/BgI,QAAQ,EAAEA,CAAA,KAAM;QACd/H,2BAA2B,CAAC,KAAK,CAAC;QAClCI,aAAa,CAAC,EAAE,CAAC;MACnB,CAAE;MACF6C,IAAI,EAAEQ,gBAAiB;MACvBX,MAAM,EAAC,QAAQ;MAAAwD,QAAA,eAEfhI,OAAA;QAAK+H,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBhI,OAAA;UAAG+H,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE7B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJpI,OAAA,CAACI,QAAQ;UACP8J,IAAI,EAAE,EAAG;UACTjG,KAAK,EAAEpC,UAAW;UAClBwH,QAAQ,EAAGc,CAAC,IAAKrI,aAAa,CAACqI,CAAC,CAACC,MAAM,CAACnG,KAAK,CAAE;UAC/C0E,WAAW,EAAC;QAAiF;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9F,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRpI,OAAA,CAAChB,KAAK;MACJsF,KAAK,EAAC,oBAAoB;MAC1BkF,IAAI,EAAE7H,oBAAqB;MAC3B8H,QAAQ,EAAEA,CAAA,KAAM7H,uBAAuB,CAAC,KAAK,CAAE;MAC/C8H,MAAM,EAAE,cACN1J,OAAA,CAACpB,MAAM;QAAc0J,OAAO,EAAEA,CAAA,KAAM1G,uBAAuB,CAAC,KAAK,CAAE;QAAAoG,QAAA,EAAC;MAEpE,GAFY,QAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEZ,CAAC,EACTrG,YAAY,KAAK,KAAK,gBACpB/B,OAAA,CAACH,OAAO;QAEN4C,IAAI,EAAEsD,iBAAiB,CAAC,CAAE;QAC1B4B,OAAO,EAAED,aAAa,CAAC,CAAE;QACzB2C,QAAQ,EAAE,GAAGlI,cAAc,MAAO;QAClC4F,SAAS,EAAC,yBAAyB;QACnCO,OAAO,EAAEA,CAAA,KAAM;UACbxJ,OAAO,CAACgG,OAAO,CAAC,0CAA0C,CAAC;UAC3DlD,uBAAuB,CAAC,KAAK,CAAC;QAChC,CAAE;QAAAoG,QAAA,EACH;MAED,GAXM,QAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAWL,CAAC,gBAEVpI,OAAA,CAACpB,MAAM;QAAcgI,IAAI,EAAC,SAAS;QAAC0B,OAAO,EAAEjC,YAAa;QAAA2B,QAAA,EAAC;MAE3D,GAFY,QAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEZ,CACT,CACD;MAAAJ,QAAA,eAEFhI,OAAA;QAAK+H,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBhI,OAAA;UAAAgI,QAAA,gBACEhI,OAAA;YAAI+H,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnDpI,OAAA,CAACd,KAAK,CAACoL,KAAK;YACVrG,KAAK,EAAElC,YAAa;YACpBsH,QAAQ,EAAGc,CAAC,IAAKnI,eAAe,CAACmI,CAAC,CAACC,MAAM,CAACnG,KAAK,CAAE;YACjD8D,SAAS,EAAC,MAAM;YAAAC,QAAA,eAEhBhI,OAAA,CAACZ,KAAK;cAACmL,SAAS,EAAC,UAAU;cAAAvC,QAAA,gBACzBhI,OAAA,CAACd,KAAK;gBAAC+E,KAAK,EAAC,KAAK;gBAAA+D,QAAA,eAChBhI,OAAA,CAACZ,KAAK;kBAAA4I,QAAA,gBACJhI,OAAA,CAACN,iBAAiB;oBAAAuI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACrBpI,OAAA;oBAAAgI,QAAA,EAAM;kBAA4B;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACzCpI,OAAA,CAACX,OAAO;oBAACiF,KAAK,EAAC,sEAAsE;oBAAA0D,QAAA,eACnFhI,OAAA,CAACJ,kBAAkB;sBAAAqI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACRpI,OAAA,CAACd,KAAK;gBAAC+E,KAAK,EAAC,MAAM;gBAAA+D,QAAA,eACjBhI,OAAA,CAACZ,KAAK;kBAAA4I,QAAA,gBACJhI,OAAA,CAACL,gBAAgB;oBAAAsI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACpBpI,OAAA;oBAAAgI,QAAA,EAAM;kBAAiC;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9CpI,OAAA,CAACX,OAAO;oBAACiF,KAAK,EAAC,yCAAyC;oBAAA0D,QAAA,eACtDhI,OAAA,CAACJ,kBAAkB;sBAAAqI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACf,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACX,CAAC,eAENpI,OAAA;UAAAgI,QAAA,gBACEhI,OAAA;YAAI+H,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACtDpI,OAAA,CAACb,QAAQ,CAACmL,KAAK;YACbrG,KAAK,EAAEhC,YAAa;YACpBoH,QAAQ,EAAGrE,MAAM,IAAK9C,eAAe,CAAC8C,MAAkB,CAAE;YAC1D+C,SAAS,EAAC,MAAM;YAAAC,QAAA,eAEhBhI,OAAA,CAACZ,KAAK;cAACmL,SAAS,EAAC,UAAU;cAAAvC,QAAA,gBACzBhI,OAAA,CAACb,QAAQ;gBAAC8E,KAAK,EAAC,IAAI;gBAAA+D,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAClCpI,OAAA,CAACb,QAAQ;gBAAC8E,KAAK,EAAC,OAAO;gBAAA+D,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAChDpI,OAAA,CAACb,QAAQ;gBAAC8E,KAAK,EAAC,WAAW;gBAAA+D,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,EAChBnG,YAAY,CAACY,MAAM,KAAK,CAAC,iBACxB7C,OAAA,CAACjB,KAAK;YACJD,OAAO,EAAC,4CAA4C;YACpD8H,IAAI,EAAC,SAAS;YACd4B,QAAQ;YACRT,SAAS,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjB,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAENpI,OAAA;UAAAgI,QAAA,gBACEhI,OAAA;YAAI+H,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC/CpI,OAAA,CAACrB,KAAK;YACJsF,KAAK,EAAE9B,cAAe;YACtBkH,QAAQ,EAAGc,CAAC,IAAK/H,iBAAiB,CAAC+H,CAAC,CAACC,MAAM,CAACnG,KAAK,CAAE;YACnD0E,WAAW,EAAC,wBAAwB;YACpC6B,UAAU,EAAE,IAAIzI,YAAY;UAAG;YAAAkG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAENpI,OAAA,CAACjB,KAAK;UACJD,OAAO,EAAC,oBAAoB;UAC5ByJ,WAAW,EAAE,oBAAoB5H,UAAU,GAAGF,mBAAmB,CAACoC,MAAM,GAAGtC,WAAW,CAACsC,MAAM,mBAAmBd,YAAY,CAAC0I,WAAW,CAAC,CAAC,UAAW;UACrJ7D,IAAI,EAAC,MAAM;UACX4B,QAAQ;QAAA;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC9H,EAAA,CA1kBID,iBAA2B;EAAA,QAgBhBpB,IAAI,CAACqD,OAAO;AAAA;AAAAoI,EAAA,GAhBvBrK,iBAA2B;AA4kBjC,eAAeA,iBAAiB;AAAC,IAAAqK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}