{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/TestPanel.tsx\";\nimport React from 'react';\nimport { Card, Typography } from 'antd';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst TestPanel = () => {\n  return /*#__PURE__*/_jsxDEV(Card, {\n    style: {\n      margin: '16px 0'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 4,\n      children: \"Test Panel\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      children: \"This is a test panel component for development purposes.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n};\n_c = TestPanel;\nexport default TestPanel;\nvar _c;\n$RefreshReg$(_c, \"TestPanel\");", "map": {"version": 3, "names": ["React", "Card", "Typography", "jsxDEV", "_jsxDEV", "Title", "Text", "TestPanel", "style", "margin", "children", "level", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/TestPanel.tsx"], "sourcesContent": ["import React from 'react';\nimport { Card, Typography } from 'antd';\n\nconst { Title, Text } = Typography;\n\nconst TestPanel: React.FC = () => {\n  return (\n    <Card style={{ margin: '16px 0' }}>\n      <Title level={4}>Test Panel</Title>\n      <Text>This is a test panel component for development purposes.</Text>\n    </Card>\n  );\n};\n\nexport default TestPanel;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,UAAU,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGJ,UAAU;AAElC,MAAMK,SAAmB,GAAGA,CAAA,KAAM;EAChC,oBACEH,OAAA,CAACH,IAAI;IAACO,KAAK,EAAE;MAAEC,MAAM,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAChCN,OAAA,CAACC,KAAK;MAACM,KAAK,EAAE,CAAE;MAAAD,QAAA,EAAC;IAAU;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACnCX,OAAA,CAACE,IAAI;MAAAI,QAAA,EAAC;IAAwD;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACjE,CAAC;AAEX,CAAC;AAACC,EAAA,GAPIT,SAAmB;AASzB,eAAeA,SAAS;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}