[{"/Users/<USER>/Desktop/MaBourseWebsite/src/index.tsx": "1", "/Users/<USER>/Desktop/MaBourseWebsite/src/App.tsx": "2", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Scholarships.tsx": "3", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Contact.tsx": "4", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/NotFound.tsx": "5", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/About.tsx": "6", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Home.tsx": "7", "/Users/<USER>/Desktop/MaBourseWebsite/src/context/ScholarshipContext.tsx": "8", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Layout.tsx": "9", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipDetail.tsx": "10", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/ErrorBoundary.tsx": "11", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipCard.tsx": "12", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Footer.tsx": "13", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Header.tsx": "14", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/Loading.tsx": "15", "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useApi.ts": "16", "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useAuth.ts": "17", "/Users/<USER>/Desktop/MaBourseWebsite/src/context/AuthContext.tsx": "18", "/Users/<USER>/Desktop/MaBourseWebsite/src/context/LanguageContext.tsx": "19", "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/en.ts": "20", "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/fr.ts": "21", "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/ar.ts": "22", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/LanguageSwitcher.tsx": "23", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipGrid.tsx": "24", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipCard.tsx": "25", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/index.tsx": "26", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/App.tsx": "27", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/About.tsx": "28", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Home.tsx": "29", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Contact.tsx": "30", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/NotFound.tsx": "31", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Scholarships.tsx": "32", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/ScholarshipContext.tsx": "33", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/AuthContext.tsx": "34", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/LanguageContext.tsx": "35", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipDetail.tsx": "36", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Layout.tsx": "37", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/ErrorBoundary.tsx": "38", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLayout.tsx": "39", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Scholarships.tsx": "40", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Dashboard.tsx": "41", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Login.tsx": "42", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ScholarshipGrid.tsx": "43", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipCard.tsx": "44", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/fr.ts": "45", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/en.ts": "46", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/ar.ts": "47", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Header.tsx": "48", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/Loading.tsx": "49", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Footer.tsx": "50", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Sidebar.tsx": "51", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Modal.tsx": "52", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipForm.tsx": "53", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useApi.ts": "54", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/LanguageSwitcher.tsx": "55", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminManagement.tsx": "56", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Messages.tsx": "57", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminDashboard.tsx": "58", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/MessagesManager.tsx": "59", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipsManager.tsx": "60", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/NewsletterManager.tsx": "61", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Settings.tsx": "62", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ProtectedRoute.tsx": "63", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/AdminProtectedRoute.tsx": "64", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ForgotPassword.tsx": "65", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ResetPassword.tsx": "66", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorVerification.tsx": "67", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/TwoFactorSettings.tsx": "68", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorSetup.tsx": "69", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useAdminApi.ts": "70", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Analytics.tsx": "71", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AnalyticsDashboard.tsx": "72", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/EmailNotifications.tsx": "73", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/EmailNotificationSettings.tsx": "74", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/TestPanel.tsx": "75", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/apiConfig.ts": "76", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/api.ts": "77", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLoginTester.tsx": "78", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/index.tsx": "79", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/App.tsx": "80", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Scholarships.tsx": "81", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/About.tsx": "82", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/NotFound.tsx": "83", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Contact.tsx": "84", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/ScholarshipContext.tsx": "85", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/LanguageContext.tsx": "86", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Layout.tsx": "87", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipDetail.tsx": "88", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/ErrorBoundary.tsx": "89", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/NewsletterManager.tsx": "90", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AdminLayout.tsx": "91", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Settings.tsx": "92", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminManagement.tsx": "93", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminDashboard.tsx": "94", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ResetPassword.tsx": "95", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ForgotPassword.tsx": "96", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/EmailNotifications.tsx": "97", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/TwoFactorSettings.tsx": "98", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/Analytics.tsx": "99", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipGrid.tsx": "100", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipCard.tsx": "101", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/fr.ts": "102", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Footer.tsx": "103", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/en.ts": "104", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/ar.ts": "105", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Header.tsx": "106", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/Loading.tsx": "107", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/EmailNotificationSettings.tsx": "108", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/TwoFactorSetup.tsx": "109", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AnalyticsDashboard.tsx": "110", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipCard.tsx": "111", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/LanguageSwitcher.tsx": "112", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ContactForm.tsx": "113", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Modal.tsx": "114", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSubscription.tsx": "115", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/SectionHeader.tsx": "116", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/scholarshipService.ts": "117", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/icons/index.tsx": "118", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateFormatter.ts": "119", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelSection.tsx": "120", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FundingSourceSection.tsx": "121", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/HeroSection.tsx": "122", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/UniversityOrganizationSection.tsx": "123", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/GovernmentScholarshipsSection.tsx": "124", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSection.tsx": "125", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/LatestScholarshipsSection.tsx": "126", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelCategoriesSection.tsx": "127", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedHome.tsx": "128", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedStudyLevelSection.tsx": "129", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedHeroSection.tsx": "130", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedLatestScholarshipsSection.tsx": "131", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedFundingSourcesSection.tsx": "132", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/TestimonialsSection.tsx": "133", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedNewsletterSection.tsx": "134", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FeatureHighlightsSection.tsx": "135", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedScholarshipCard.tsx": "136", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedScholarshipDetailPage.tsx": "137", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/slugify.ts": "138", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/envValidator.ts": "139", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateUtils.ts": "140", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/config/axiosConfig.ts": "141", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/contexts/SecureAuthContext.tsx": "142", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/secureApiService.ts": "143", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/SecureLogin.tsx": "144", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/AccountRecovery.tsx": "145", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/SecureAdminProtectedRoute.tsx": "146", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/SecurityDashboard.tsx": "147", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/AuthFeedback.tsx": "148", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/AuthDebugger.tsx": "149", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/TestPanel.tsx": "150", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/AuthMigrationNotice.tsx": "151", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/clearStorage.ts": "152", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/api.ts": "153", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/hooks/useAdminApi.ts": "154"}, {"size": 274, "mtime": *************, "results": "155", "hashOfConfig": "156"}, {"size": 2118, "mtime": *************, "results": "157", "hashOfConfig": "156"}, {"size": 5438, "mtime": 1745977740794, "results": "158", "hashOfConfig": "156"}, {"size": 10242, "mtime": 1745981563339, "results": "159", "hashOfConfig": "156"}, {"size": 1742, "mtime": 1745977713025, "results": "160", "hashOfConfig": "156"}, {"size": 7131, "mtime": 1745981562258, "results": "161", "hashOfConfig": "156"}, {"size": 10498, "mtime": 1745982258490, "results": "162", "hashOfConfig": "156"}, {"size": 3076, "mtime": 1745945973317, "results": "163", "hashOfConfig": "156"}, {"size": 553, "mtime": 1745978322072, "results": "164", "hashOfConfig": "156"}, {"size": 6250, "mtime": 1745977773426, "results": "165", "hashOfConfig": "156"}, {"size": 2446, "mtime": 1745945132621, "results": "166", "hashOfConfig": "156"}, {"size": 2645, "mtime": 1745977581979, "results": "167", "hashOfConfig": "156"}, {"size": 4167, "mtime": 1745981843303, "results": "168", "hashOfConfig": "156"}, {"size": 5713, "mtime": 1745981758114, "results": "169", "hashOfConfig": "156"}, {"size": 675, "mtime": 1745976791748, "results": "170", "hashOfConfig": "156"}, {"size": 1059, "mtime": 1745976720607, "results": "171", "hashOfConfig": "156"}, {"size": 3452, "mtime": 1745946003719, "results": "172", "hashOfConfig": "156"}, {"size": 2518, "mtime": 1745983866923, "results": "173", "hashOfConfig": "156"}, {"size": 1737, "mtime": 1745978376608, "results": "174", "hashOfConfig": "156"}, {"size": 4075, "mtime": 1745982269507, "results": "175", "hashOfConfig": "156"}, {"size": 4531, "mtime": 1745982263875, "results": "176", "hashOfConfig": "156"}, {"size": 5406, "mtime": 1745982274929, "results": "177", "hashOfConfig": "156"}, {"size": 2535, "mtime": 1745978386143, "results": "178", "hashOfConfig": "156"}, {"size": 737, "mtime": 1745944438688, "results": "179", "hashOfConfig": "156"}, {"size": 2323, "mtime": 1745982233889, "results": "180", "hashOfConfig": "156"}, {"size": 274, "mtime": *************, "results": "181", "hashOfConfig": "182"}, {"size": 4324, "mtime": 1746276088446, "results": "183", "hashOfConfig": "182"}, {"size": 7131, "mtime": 1745981562258, "results": "184", "hashOfConfig": "182"}, {"size": 10498, "mtime": 1745982258490, "results": "185", "hashOfConfig": "182"}, {"size": 10242, "mtime": 1745981563339, "results": "186", "hashOfConfig": "182"}, {"size": 1742, "mtime": 1745977713025, "results": "187", "hashOfConfig": "182"}, {"size": 8315, "mtime": 1746204095547, "results": "188", "hashOfConfig": "182"}, {"size": 3076, "mtime": 1745945973317, "results": "189", "hashOfConfig": "182"}, {"size": 2535, "mtime": 1746033992389, "results": "190", "hashOfConfig": "182"}, {"size": 1737, "mtime": 1745978376608, "results": "191", "hashOfConfig": "182"}, {"size": 6250, "mtime": 1745977773426, "results": "192", "hashOfConfig": "182"}, {"size": 553, "mtime": 1745978322072, "results": "193", "hashOfConfig": "182"}, {"size": 2446, "mtime": 1745945132621, "results": "194", "hashOfConfig": "182"}, {"size": 5154, "mtime": 1746276100698, "results": "195", "hashOfConfig": "182"}, {"size": 8097, "mtime": 1745983506688, "results": "196", "hashOfConfig": "182"}, {"size": 6024, "mtime": 1746026061709, "results": "197", "hashOfConfig": "182"}, {"size": 9559, "mtime": 1746270350218, "results": "198", "hashOfConfig": "182"}, {"size": 737, "mtime": 1745944438688, "results": "199", "hashOfConfig": "182"}, {"size": 2645, "mtime": 1745977581979, "results": "200", "hashOfConfig": "182"}, {"size": 4531, "mtime": 1745982263875, "results": "201", "hashOfConfig": "182"}, {"size": 4075, "mtime": 1745982269507, "results": "202", "hashOfConfig": "182"}, {"size": 5406, "mtime": 1745982274929, "results": "203", "hashOfConfig": "182"}, {"size": 5713, "mtime": 1745981758114, "results": "204", "hashOfConfig": "182"}, {"size": 675, "mtime": 1745976791748, "results": "205", "hashOfConfig": "182"}, {"size": 4167, "mtime": 1745981843303, "results": "206", "hashOfConfig": "182"}, {"size": 2115, "mtime": 1746029576846, "results": "207", "hashOfConfig": "182"}, {"size": 1689, "mtime": 1745982730905, "results": "208", "hashOfConfig": "182"}, {"size": 19288, "mtime": 1745983400384, "results": "209", "hashOfConfig": "182"}, {"size": 1059, "mtime": 1745976720607, "results": "210", "hashOfConfig": "182"}, {"size": 2535, "mtime": 1745978386143, "results": "211", "hashOfConfig": "182"}, {"size": 14052, "mtime": 1746274142489, "results": "212", "hashOfConfig": "182"}, {"size": 3939, "mtime": 1746017528736, "results": "213", "hashOfConfig": "182"}, {"size": 13857, "mtime": 1746282401482, "results": "214", "hashOfConfig": "182"}, {"size": 11940, "mtime": 1746252382064, "results": "215", "hashOfConfig": "182"}, {"size": 38877, "mtime": 1746252407519, "results": "216", "hashOfConfig": "182"}, {"size": 8393, "mtime": 1746249939564, "results": "217", "hashOfConfig": "182"}, {"size": 11544, "mtime": 1746272209267, "results": "218", "hashOfConfig": "182"}, {"size": 1343, "mtime": 1746033436995, "results": "219", "hashOfConfig": "182"}, {"size": 1752, "mtime": 1746274101102, "results": "220", "hashOfConfig": "182"}, {"size": 3341, "mtime": 1746199132190, "results": "221", "hashOfConfig": "182"}, {"size": 7045, "mtime": 1746199160974, "results": "222", "hashOfConfig": "182"}, {"size": 4103, "mtime": 1746200520123, "results": "223", "hashOfConfig": "182"}, {"size": 6276, "mtime": 1746249196201, "results": "224", "hashOfConfig": "182"}, {"size": 7222, "mtime": 1746249216418, "results": "225", "hashOfConfig": "182"}, {"size": 1105, "mtime": 1746201832350, "results": "226", "hashOfConfig": "182"}, {"size": 921, "mtime": 1746202207790, "results": "227", "hashOfConfig": "182"}, {"size": 11379, "mtime": 1746276323665, "results": "228", "hashOfConfig": "182"}, {"size": 1059, "mtime": 1746226321253, "results": "229", "hashOfConfig": "182"}, {"size": 7914, "mtime": 1746251582912, "results": "230", "hashOfConfig": "182"}, {"size": 4784, "mtime": 1746252717773, "results": "231", "hashOfConfig": "182"}, {"size": 1777, "mtime": 1746254015165, "results": "232", "hashOfConfig": "182"}, {"size": 9435, "mtime": 1746252638103, "results": "233", "hashOfConfig": "182"}, {"size": 5504, "mtime": 1746275135511, "results": "234", "hashOfConfig": "182"}, {"size": 620, "mtime": 1747275499301, "results": "235", "hashOfConfig": "236"}, {"size": 4709, "mtime": 1752253224365, "results": "237", "hashOfConfig": "236"}, {"size": 13828, "mtime": 1752248890943, "results": "238", "hashOfConfig": "236"}, {"size": 19195, "mtime": 1747280789154, "results": "239", "hashOfConfig": "236"}, {"size": 3587, "mtime": 1747235972243, "results": "240", "hashOfConfig": "236"}, {"size": 10653, "mtime": 1747235897205, "results": "241", "hashOfConfig": "236"}, {"size": 3076, "mtime": 1745945973317, "results": "242", "hashOfConfig": "236"}, {"size": 1974, "mtime": 1747278879917, "results": "243", "hashOfConfig": "236"}, {"size": 553, "mtime": 1745978322072, "results": "244", "hashOfConfig": "236"}, {"size": 18635, "mtime": 1747235596822, "results": "245", "hashOfConfig": "246"}, {"size": 2446, "mtime": 1745945132621, "results": "247", "hashOfConfig": "236"}, {"size": 19874, "mtime": 1752251800113, "results": "248", "hashOfConfig": "236"}, {"size": 5619, "mtime": 1747278582985, "results": "249", "hashOfConfig": "236"}, {"size": 11544, "mtime": 1746272209267, "results": "250", "hashOfConfig": "236"}, {"size": 14051, "mtime": 1747277440650, "results": "251", "hashOfConfig": "236"}, {"size": 14143, "mtime": 1747150652537, "results": "252", "hashOfConfig": "236"}, {"size": 7045, "mtime": 1746199160974, "results": "253", "hashOfConfig": "236"}, {"size": 3341, "mtime": 1746199132190, "results": "254", "hashOfConfig": "236"}, {"size": 1059, "mtime": 1746226321253, "results": "255", "hashOfConfig": "236"}, {"size": 6276, "mtime": 1746249196201, "results": "256", "hashOfConfig": "236"}, {"size": 921, "mtime": 1746202207790, "results": "257", "hashOfConfig": "236"}, {"size": 1695, "mtime": 1747186871230, "results": "258", "hashOfConfig": "246"}, {"size": 3161, "mtime": 1747232764014, "results": "259", "hashOfConfig": "246"}, {"size": 5667, "mtime": 1747280262786, "results": "260", "hashOfConfig": "236"}, {"size": 12973, "mtime": 1747224498475, "results": "261", "hashOfConfig": "236"}, {"size": 5107, "mtime": 1747280292414, "results": "262", "hashOfConfig": "236"}, {"size": 6747, "mtime": 1747280339749, "results": "263", "hashOfConfig": "236"}, {"size": 5713, "mtime": 1745981758114, "results": "264", "hashOfConfig": "236"}, {"size": 675, "mtime": 1745976791748, "results": "265", "hashOfConfig": "246"}, {"size": 7860, "mtime": 1752251788545, "results": "266", "hashOfConfig": "236"}, {"size": 7222, "mtime": 1746249216418, "results": "267", "hashOfConfig": "236"}, {"size": 9886, "mtime": 1752254531881, "results": "268", "hashOfConfig": "236"}, {"size": 3866, "mtime": 1747221404207, "results": "269", "hashOfConfig": "246"}, {"size": 2535, "mtime": 1745978386143, "results": "270", "hashOfConfig": "236"}, {"size": 3877, "mtime": 1747235933700, "results": "271", "hashOfConfig": "236"}, {"size": 1689, "mtime": 1745982730905, "results": "272", "hashOfConfig": "236"}, {"size": 3697, "mtime": 1747184461868, "results": "273", "hashOfConfig": "246"}, {"size": 959, "mtime": 1747186815101, "results": "274", "hashOfConfig": "246"}, {"size": 5906, "mtime": 1752248869494, "results": "275", "hashOfConfig": "236"}, {"size": 2847, "mtime": 1747187027857, "results": "276", "hashOfConfig": "236"}, {"size": 2604, "mtime": 1747279467729, "results": "277", "hashOfConfig": "236"}, {"size": 8119, "mtime": 1747220020952, "results": "278", "hashOfConfig": "246"}, {"size": 8243, "mtime": 1747220059414, "results": "279", "hashOfConfig": "246"}, {"size": 3071, "mtime": 1747221577347, "results": "280", "hashOfConfig": "246"}, {"size": 6125, "mtime": 1747221750779, "results": "281", "hashOfConfig": "246"}, {"size": 6017, "mtime": 1747221715802, "results": "282", "hashOfConfig": "246"}, {"size": 3890, "mtime": 1747221780672, "results": "283", "hashOfConfig": "246"}, {"size": 3377, "mtime": 1747221613654, "results": "284", "hashOfConfig": "246"}, {"size": 3156, "mtime": 1747221640258, "results": "285", "hashOfConfig": "246"}, {"size": 7752, "mtime": 1747237735157, "results": "286", "hashOfConfig": "236"}, {"size": 11148, "mtime": 1747241866444, "results": "287", "hashOfConfig": "236"}, {"size": 9820, "mtime": 1747279005156, "results": "288", "hashOfConfig": "236"}, {"size": 8196, "mtime": 1747279261336, "results": "289", "hashOfConfig": "236"}, {"size": 9024, "mtime": 1747241959417, "results": "290", "hashOfConfig": "236"}, {"size": 9620, "mtime": 1747242054549, "results": "291", "hashOfConfig": "236"}, {"size": 9567, "mtime": 1747242099457, "results": "292", "hashOfConfig": "236"}, {"size": 5262, "mtime": 1747242002891, "results": "293", "hashOfConfig": "236"}, {"size": 5327, "mtime": 1747280365842, "results": "294", "hashOfConfig": "236"}, {"size": 28904, "mtime": 1752248907873, "results": "295", "hashOfConfig": "236"}, {"size": 1536, "mtime": 1747237627552, "results": "296", "hashOfConfig": "236"}, {"size": 2783, "mtime": 1747275467037, "results": "297", "hashOfConfig": "236"}, {"size": 4730, "mtime": 1747275582856, "results": "298", "hashOfConfig": "236"}, {"size": 5097, "mtime": 1747302359006, "results": "299", "hashOfConfig": "246"}, {"size": 8099, "mtime": 1752250846938, "results": "300", "hashOfConfig": "236"}, {"size": 12892, "mtime": 1752253063300, "results": "301", "hashOfConfig": "236"}, {"size": 9481, "mtime": 1752250083763, "results": "302", "hashOfConfig": "236"}, {"size": 11164, "mtime": 1747280695673, "results": "303", "hashOfConfig": "236"}, {"size": 1840, "mtime": 1747320082170, "results": "304", "hashOfConfig": "236"}, {"size": 9696, "mtime": 1747280682590, "results": "305", "hashOfConfig": "236"}, {"size": 5157, "mtime": 1747277968202, "results": "306", "hashOfConfig": "236"}, {"size": 4796, "mtime": 1752250600870, "results": "307", "hashOfConfig": "236"}, {"size": 358, "mtime": 1752254238558, "results": "308", "hashOfConfig": "236"}, {"size": 436, "mtime": 1752254249254, "results": "309", "hashOfConfig": "236"}, {"size": 1228, "mtime": 1752254327493, "results": "310", "hashOfConfig": "236"}, {"size": 3337, "mtime": 1752254433306, "results": "311", "hashOfConfig": "236"}, {"size": 1441, "mtime": 1752254453212, "results": "312", "hashOfConfig": "236"}, {"filePath": "313", "messages": "314", "suppressedMessages": "315", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "poe9py", {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "14ofb3m", {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "qkekr7", {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "15319ot", {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Desktop/MaBourseWebsite/src/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/App.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Scholarships.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Contact.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/NotFound.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/About.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Home.tsx", ["775"], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/context/ScholarshipContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipDetail.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Footer.tsx", ["776", "777", "778"], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/Loading.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useAuth.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/context/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/context/LanguageContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/en.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/fr.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/ar.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/LanguageSwitcher.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipGrid.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/App.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/About.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Home.tsx", ["779"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Contact.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/NotFound.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Scholarships.tsx", ["780"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/ScholarshipContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/LanguageContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipDetail.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLayout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Scholarships.tsx", ["781", "782"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Dashboard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Login.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ScholarshipGrid.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/fr.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/en.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/ar.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/Loading.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Footer.tsx", ["783", "784", "785"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Sidebar.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Modal.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipForm.tsx", ["786"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/LanguageSwitcher.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminManagement.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Messages.tsx", ["787", "788"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminDashboard.tsx", ["789", "790", "791"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/MessagesManager.tsx", [], ["792"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipsManager.tsx", ["793"], ["794"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/NewsletterManager.tsx", [], ["795"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Settings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/AdminProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ForgotPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ResetPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorVerification.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/TwoFactorSettings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorSetup.tsx", [], ["796"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useAdminApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Analytics.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AnalyticsDashboard.tsx", [], ["797"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/EmailNotifications.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/EmailNotificationSettings.tsx", ["798"], ["799"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/TestPanel.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/apiConfig.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/api.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLoginTester.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/App.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Scholarships.tsx", ["800"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/About.tsx", ["801", "802"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/NotFound.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Contact.tsx", ["803", "804", "805"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/ScholarshipContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/LanguageContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipDetail.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/NewsletterManager.tsx", ["806", "807"], ["808"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AdminLayout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Settings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminManagement.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminDashboard.tsx", ["809", "810"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ResetPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ForgotPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/EmailNotifications.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/TwoFactorSettings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/Analytics.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipGrid.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/fr.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Footer.tsx", ["811", "812", "813", "814"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/en.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/ar.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/Loading.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/EmailNotificationSettings.tsx", [], ["815"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/TwoFactorSetup.tsx", [], ["816"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AnalyticsDashboard.tsx", ["817"], ["818"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipCard.tsx", ["819"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/LanguageSwitcher.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ContactForm.tsx", ["820"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Modal.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSubscription.tsx", ["821"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/SectionHeader.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/scholarshipService.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/icons/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateFormatter.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FundingSourceSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/HeroSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/UniversityOrganizationSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/GovernmentScholarshipsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/LatestScholarshipsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelCategoriesSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedHome.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedStudyLevelSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedHeroSection.tsx", ["822"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedLatestScholarshipsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedFundingSourcesSection.tsx", ["823"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/TestimonialsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedNewsletterSection.tsx", ["824", "825"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FeatureHighlightsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedScholarshipDetailPage.tsx", ["826", "827"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/slugify.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/envValidator.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateUtils.ts", ["828"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/config/axiosConfig.ts", ["829"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/contexts/SecureAuthContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/secureApiService.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/SecureLogin.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/AccountRecovery.tsx", ["830"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/SecureAdminProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/SecurityDashboard.tsx", ["831", "832"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/AuthFeedback.tsx", ["833"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/AuthDebugger.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/TestPanel.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/AuthMigrationNotice.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/clearStorage.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/api.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/hooks/useAdminApi.ts", [], [], {"ruleId": "834", "severity": 1, "message": "835", "line": 170, "column": 7, "nodeType": "836", "messageId": "837", "endLine": 170, "endColumn": 15}, {"ruleId": "838", "severity": 1, "message": "839", "line": 51, "column": 15, "nodeType": "840", "endLine": 51, "endColumn": 74}, {"ruleId": "838", "severity": 1, "message": "839", "line": 57, "column": 15, "nodeType": "840", "endLine": 57, "endColumn": 74}, {"ruleId": "838", "severity": 1, "message": "839", "line": 63, "column": 15, "nodeType": "840", "endLine": 63, "endColumn": 74}, {"ruleId": "834", "severity": 1, "message": "835", "line": 170, "column": 7, "nodeType": "836", "messageId": "837", "endLine": 170, "endColumn": 15}, {"ruleId": "841", "severity": 1, "message": "842", "line": 44, "column": 6, "nodeType": "843", "endLine": 44, "endColumn": 68, "suggestions": "844"}, {"ruleId": "834", "severity": 1, "message": "845", "line": 3, "column": 8, "nodeType": "836", "messageId": "837", "endLine": 3, "endColumn": 19}, {"ruleId": "834", "severity": 1, "message": "846", "line": 31, "column": 11, "nodeType": "836", "messageId": "837", "endLine": 31, "endColumn": 23}, {"ruleId": "838", "severity": 1, "message": "839", "line": 51, "column": 15, "nodeType": "840", "endLine": 51, "endColumn": 74}, {"ruleId": "838", "severity": 1, "message": "839", "line": 57, "column": 15, "nodeType": "840", "endLine": 57, "endColumn": 74}, {"ruleId": "838", "severity": 1, "message": "839", "line": 63, "column": 15, "nodeType": "840", "endLine": 63, "endColumn": 74}, {"ruleId": "834", "severity": 1, "message": "846", "line": 34, "column": 11, "nodeType": "836", "messageId": "837", "endLine": 34, "endColumn": 23}, {"ruleId": "834", "severity": 1, "message": "847", "line": 10, "column": 3, "nodeType": "836", "messageId": "837", "endLine": 10, "endColumn": 8}, {"ruleId": "834", "severity": 1, "message": "848", "line": 15, "column": 24, "nodeType": "836", "messageId": "837", "endLine": 15, "endColumn": 43}, {"ruleId": "834", "severity": 1, "message": "849", "line": 3, "column": 10, "nodeType": "836", "messageId": "837", "endLine": 3, "endColumn": 21}, {"ruleId": "834", "severity": 1, "message": "850", "line": 44, "column": 9, "nodeType": "836", "messageId": "837", "endLine": 44, "endColumn": 17}, {"ruleId": "851", "severity": 1, "message": "852", "line": 162, "column": 36, "nodeType": "853", "messageId": "854", "endLine": 162, "endColumn": 60}, {"ruleId": "841", "severity": 1, "message": "855", "line": 37, "column": 6, "nodeType": "843", "endLine": 37, "endColumn": 42, "suggestions": "856", "suppressions": "857"}, {"ruleId": "834", "severity": 1, "message": "858", "line": 236, "column": 9, "nodeType": "836", "messageId": "837", "endLine": 236, "endColumn": 30}, {"ruleId": "841", "severity": 1, "message": "855", "line": 64, "column": 6, "nodeType": "843", "endLine": 64, "endColumn": 61, "suggestions": "859", "suppressions": "860"}, {"ruleId": "841", "severity": 1, "message": "855", "line": 29, "column": 6, "nodeType": "843", "endLine": 29, "endColumn": 31, "suggestions": "861", "suppressions": "862"}, {"ruleId": "841", "severity": 1, "message": "863", "line": 25, "column": 6, "nodeType": "843", "endLine": 25, "endColumn": 8, "suggestions": "864", "suppressions": "865"}, {"ruleId": "841", "severity": 1, "message": "866", "line": 177, "column": 6, "nodeType": "843", "endLine": 177, "endColumn": 8, "suggestions": "867", "suppressions": "868"}, {"ruleId": "834", "severity": 1, "message": "849", "line": 3, "column": 10, "nodeType": "836", "messageId": "837", "endLine": 3, "endColumn": 21}, {"ruleId": "841", "severity": 1, "message": "869", "line": 37, "column": 6, "nodeType": "843", "endLine": 37, "endColumn": 8, "suggestions": "870", "suppressions": "871"}, {"ruleId": "841", "severity": 1, "message": "842", "line": 44, "column": 6, "nodeType": "843", "endLine": 44, "endColumn": 68, "suggestions": "872"}, {"ruleId": "838", "severity": 1, "message": "839", "line": 135, "column": 21, "nodeType": "840", "endLine": 135, "endColumn": 96}, {"ruleId": "838", "severity": 1, "message": "839", "line": 141, "column": 21, "nodeType": "840", "endLine": 141, "endColumn": 96}, {"ruleId": "838", "severity": 1, "message": "839", "line": 108, "column": 21, "nodeType": "840", "endLine": 108, "endColumn": 94}, {"ruleId": "838", "severity": 1, "message": "839", "line": 114, "column": 21, "nodeType": "840", "endLine": 114, "endColumn": 94}, {"ruleId": "838", "severity": 1, "message": "839", "line": 120, "column": 21, "nodeType": "840", "endLine": 120, "endColumn": 94}, {"ruleId": "834", "severity": 1, "message": "873", "line": 2, "column": 105, "nodeType": "836", "messageId": "837", "endLine": 2, "endColumn": 111}, {"ruleId": "834", "severity": 1, "message": "874", "line": 3, "column": 111, "nodeType": "836", "messageId": "837", "endLine": 3, "endColumn": 126}, {"ruleId": "841", "severity": 1, "message": "855", "line": 45, "column": 6, "nodeType": "843", "endLine": 45, "endColumn": 31, "suggestions": "875", "suppressions": "876"}, {"ruleId": "834", "severity": 1, "message": "850", "line": 44, "column": 9, "nodeType": "836", "messageId": "837", "endLine": 44, "endColumn": 17}, {"ruleId": "841", "severity": 1, "message": "877", "line": 136, "column": 6, "nodeType": "843", "endLine": 136, "endColumn": 8, "suggestions": "878"}, {"ruleId": "838", "severity": 1, "message": "839", "line": 117, "column": 17, "nodeType": "840", "endLine": 117, "endColumn": 131}, {"ruleId": "838", "severity": 1, "message": "839", "line": 125, "column": 17, "nodeType": "840", "endLine": 125, "endColumn": 131}, {"ruleId": "838", "severity": 1, "message": "839", "line": 133, "column": 17, "nodeType": "840", "endLine": 133, "endColumn": 131}, {"ruleId": "838", "severity": 1, "message": "839", "line": 141, "column": 17, "nodeType": "840", "endLine": 141, "endColumn": 131}, {"ruleId": "841", "severity": 1, "message": "869", "line": 37, "column": 6, "nodeType": "843", "endLine": 37, "endColumn": 8, "suggestions": "879", "suppressions": "880"}, {"ruleId": "841", "severity": 1, "message": "863", "line": 25, "column": 6, "nodeType": "843", "endLine": 25, "endColumn": 8, "suggestions": "881", "suppressions": "882"}, {"ruleId": "834", "severity": 1, "message": "883", "line": 15, "column": 10, "nodeType": "836", "messageId": "837", "endLine": 15, "endColumn": 13}, {"ruleId": "841", "severity": 1, "message": "866", "line": 129, "column": 6, "nodeType": "843", "endLine": 129, "endColumn": 8, "suggestions": "884", "suppressions": "885"}, {"ruleId": "834", "severity": 1, "message": "846", "line": 26, "column": 11, "nodeType": "836", "messageId": "837", "endLine": 26, "endColumn": 23}, {"ruleId": "838", "severity": 1, "message": "839", "line": 101, "column": 26, "nodeType": "840", "endLine": 101, "endColumn": 87}, {"ruleId": "834", "severity": 1, "message": "886", "line": 2, "column": 40, "nodeType": "836", "messageId": "837", "endLine": 2, "endColumn": 44}, {"ruleId": "841", "severity": 1, "message": "887", "line": 38, "column": 6, "nodeType": "843", "endLine": 38, "endColumn": 8, "suggestions": "888"}, {"ruleId": "834", "severity": 1, "message": "889", "line": 25, "column": 9, "nodeType": "836", "messageId": "837", "endLine": 25, "endColumn": 21}, {"ruleId": "838", "severity": 1, "message": "839", "line": 186, "column": 21, "nodeType": "840", "endLine": 186, "endColumn": 74}, {"ruleId": "838", "severity": 1, "message": "839", "line": 188, "column": 21, "nodeType": "840", "endLine": 188, "endColumn": 74}, {"ruleId": "834", "severity": 1, "message": "886", "line": 4, "column": 30, "nodeType": "836", "messageId": "837", "endLine": 4, "endColumn": 34}, {"ruleId": "834", "severity": 1, "message": "890", "line": 7, "column": 10, "nodeType": "836", "messageId": "837", "endLine": 7, "endColumn": 16}, {"ruleId": "891", "severity": 1, "message": "892", "line": 172, "column": 1, "nodeType": "893", "endLine": 179, "endColumn": 3}, {"ruleId": "834", "severity": 1, "message": "890", "line": 2, "column": 10, "nodeType": "836", "messageId": "837", "endLine": 2, "endColumn": 16}, {"ruleId": "841", "severity": 1, "message": "894", "line": 43, "column": 6, "nodeType": "843", "endLine": 43, "endColumn": 26, "suggestions": "895"}, {"ruleId": "834", "severity": 1, "message": "896", "line": 13, "column": 10, "nodeType": "836", "messageId": "837", "endLine": 13, "endColumn": 20}, {"ruleId": "841", "severity": 1, "message": "897", "line": 105, "column": 6, "nodeType": "843", "endLine": 105, "endColumn": 8, "suggestions": "898"}, {"ruleId": "834", "severity": 1, "message": "899", "line": 5, "column": 15, "nodeType": "836", "messageId": "837", "endLine": 5, "endColumn": 20}, "@typescript-eslint/no-unused-vars", "'features' is assigned a value but never used.", "Identifier", "unusedVar", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchScholarships'. Either include it or remove the dependency array.", "ArrayExpression", ["900"], "'AdminLayout' is defined but never used.", "'translations' is assigned a value but never used.", "'Space' is defined but never used.", "'CheckCircleOutlined' is defined but never used.", "'useAdminApi' is defined but never used.", "'navigate' is assigned a value but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'id'.", "ArrowFunctionExpression", "unsafeRefs", "React Hook useEffect has a missing dependency: 'applyFilters'. Either include it or remove the dependency array.", ["901"], ["902"], "'sendEmailNotification' is assigned a value but never used.", ["903"], ["904"], ["905"], ["906"], "React Hook useEffect has a missing dependency: 'initializeSetup'. Either include it or remove the dependency array.", ["907"], ["908"], "React Hook useEffect has a missing dependency: 'fetchAnalyticsData'. Either include it or remove the dependency array.", ["909"], ["910"], "React Hook useEffect has a missing dependency: 'fetchSettings'. Either include it or remove the dependency array.", ["911"], ["912"], ["913"], "'Select' is defined but never used.", "'FilePdfOutlined' is defined but never used.", ["914"], ["915"], "React Hook useEffect has missing dependencies: 'adminInfo' and 'stats'. Either include them or remove the dependency array.", ["916"], ["917"], ["918"], ["919"], ["920"], "'api' is defined but never used.", ["921"], ["922"], "'Spin' is defined but never used.", "React Hook useEffect has a missing dependency: 'backgrounds.length'. Either include it or remove the dependency array.", ["923"], "'scrollToTabs' is assigned a value but never used.", "'getEnv' is defined but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "React Hook useEffect has a missing dependency: 'verifyToken'. Either include it or remove the dependency array.", ["924"], "'DateFormat' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSecurityEvents'. Either include it or remove the dependency array.", ["925"], "'Title' is assigned a value but never used.", {"desc": "926", "fix": "927"}, {"desc": "928", "fix": "929"}, {"kind": "930", "justification": "931"}, {"desc": "932", "fix": "933"}, {"kind": "930", "justification": "931"}, {"desc": "934", "fix": "935"}, {"kind": "930", "justification": "931"}, {"desc": "936", "fix": "937"}, {"kind": "930", "justification": "931"}, {"desc": "938", "fix": "939"}, {"kind": "930", "justification": "931"}, {"desc": "940", "fix": "941"}, {"kind": "930", "justification": "931"}, {"desc": "926", "fix": "942"}, {"desc": "934", "fix": "943"}, {"kind": "930", "justification": "931"}, {"desc": "944", "fix": "945"}, {"desc": "940", "fix": "946"}, {"kind": "930", "justification": "931"}, {"desc": "936", "fix": "947"}, {"kind": "930", "justification": "931"}, {"desc": "938", "fix": "948"}, {"kind": "930", "justification": "931"}, {"desc": "949", "fix": "950"}, {"desc": "951", "fix": "952"}, {"desc": "953", "fix": "954"}, "Update the dependencies array to be: [pagination.page, selectedLevel, selectedCountry, searchQuery, fetchScholarships]", {"range": "955", "text": "956"}, "Update the dependencies array to be: [applyFilters, messages, searchTerm, statusFilter]", {"range": "957", "text": "958"}, "directive", "", "Update the dependencies array to be: [scholarships, searchTerm, filterStatus, filterCountry, applyFilters]", {"range": "959", "text": "960"}, "Update the dependencies array to be: [subscribers, searchTerm, applyFilters]", {"range": "961", "text": "962"}, "Update the dependencies array to be: [initializeSetup]", {"range": "963", "text": "964"}, "Update the dependencies array to be: [fetchAnalyticsData]", {"range": "965", "text": "966"}, "Update the dependencies array to be: [fetchSettings]", {"range": "967", "text": "968"}, {"range": "969", "text": "956"}, {"range": "970", "text": "962"}, "Update the dependencies array to be: [adminInfo, stats]", {"range": "971", "text": "972"}, {"range": "973", "text": "968"}, {"range": "974", "text": "964"}, {"range": "975", "text": "966"}, "Update the dependencies array to be: [backgrounds.length]", {"range": "976", "text": "977"}, "Update the dependencies array to be: [token, accountType, verifyToken]", {"range": "978", "text": "979"}, "Update the dependencies array to be: [fetchSecurityEvents]", {"range": "980", "text": "981"}, [1225, 1287], "[pagination.page, selectedLevel, selectedCountry, searchQuery, fetchScholarships]", [1158, 1194], "[applyFilters, messages, searchTerm, statusFilter]", [1966, 2021], "[scholarships, searchTerm, filterStatus, filterCountry, applyFilters]", [903, 928], "[subscribers, searchTerm, applyFilters]", [969, 971], "[initializeSetup]", [6083, 6085], "[fetchAnalyticsData]", [1255, 1257], "[fetchSettings]", [1228, 1290], [1914, 1939], [4624, 4626], "[adminInfo, stats]", [1201, 1203], [969, 971], [4590, 4592], [1326, 1328], "[backgrounds.length]", [1455, 1475], "[token, accountType, verifyToken]", [3161, 3163], "[fetchSecurityEvents]"]