import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import Layout from './components/layout/Layout';
import EnhancedHome from './pages/EnhancedHome';
import Scholarships from './pages/Scholarships';
import About from './pages/About';
import Contact from './pages/Contact';
import NotFound from './pages/NotFound';
import EnhancedScholarshipDetailPage from './pages/EnhancedScholarshipDetailPage';
import { ScholarshipProvider } from './context/ScholarshipContext';
import { LanguageProvider } from './context/LanguageContext';
import { SecureAuthProvider } from './contexts/SecureAuthContext';
import ErrorBoundary from './components/common/ErrorBoundary';
import SecureAdminProtectedRoute from './components/SecureAdminProtectedRoute';
import TestPanel from './components/common/TestPanel';

// Admin Components
import AdminLayout from './admin/components/AdminLayout';

import SecureAdminLogin from './admin/pages/SecureLogin';
import AdminDashboard from './admin/pages/AdminDashboard';
import NewsletterManager from './admin/components/NewsletterManager';
import AdminManagement from './admin/pages/AdminManagement';
import Settings from './admin/components/Settings';
import ForgotPassword from './admin/pages/ForgotPassword';
import ResetPassword from './admin/pages/ResetPassword';
import TwoFactorSettings from './admin/pages/TwoFactorSettings';
import Analytics from './admin/pages/Analytics';
import EmailNotifications from './admin/pages/EmailNotifications';
import AccountRecovery from './pages/AccountRecovery';
import SecurityDashboard from './admin/pages/SecurityDashboard';

function App() {
  return (
    <ErrorBoundary>
      <LanguageProvider>
        <SecureAuthProvider>
          <ScholarshipProvider>
                <Router>
                  <Routes>
                  {/* Public Routes */}
                  <Route path="/" element={<Layout />}>
                    <Route index element={<EnhancedHome />} />
                    <Route path="scholarships" element={<Scholarships />} />
                    <Route path="scholarships/:id" element={<EnhancedScholarshipDetailPage />} />
                    <Route path="bourse/:slug" element={<EnhancedScholarshipDetailPage />} />
                    <Route path="about" element={<About />} />
                    <Route path="contact" element={<Contact />} />
                    <Route path="*" element={<NotFound />} />
                  </Route>

                  {/* Admin Routes */}
                  <Route path="/admin" element={<SecureAdminProtectedRoute><AdminLayout /></SecureAdminProtectedRoute>}>
                    <Route index element={<Navigate to="dashboard" replace />} />
                    <Route path="dashboard" element={<AdminDashboard />} />
                    <Route path="analytics" element={<Analytics />} />
                    <Route path="scholarships" element={<AdminDashboard />} />
                    <Route path="messages" element={<AdminDashboard />} />
                    <Route path="newsletter" element={<NewsletterManager />} />
                    <Route path="email-notifications" element={<EmailNotifications />} />
                    <Route path="admins" element={<SecureAdminProtectedRoute requireMainAdmin><AdminManagement /></SecureAdminProtectedRoute>} />
                    <Route path="security-dashboard" element={<SecureAdminProtectedRoute requireMainAdmin><SecurityDashboard /></SecureAdminProtectedRoute>} />
                    <Route path="settings" element={<Settings />} />
                    <Route path="security" element={<TwoFactorSettings />} />
                  </Route>
                  <Route path="/admin/login" element={<Navigate to="/admin/secure-login" replace />} />
                  <Route path="/admin/secure-login" element={<SecureAdminLogin />} />
                  <Route path="/admin/forgot-password" element={<ForgotPassword />} />
                  <Route path="/admin/reset-password/:token" element={<ResetPassword />} />
                  <Route path="/account-recovery" element={<AccountRecovery />} />
                  <Route path="/account-recovery/:accountType/:token" element={<AccountRecovery />} />

                  {/* Test Panel Route - only available in development mode */}
                  {process.env.NODE_ENV === 'development' && (
                    <>
                      <Route path="/test-panel" element={<TestPanel />} />

                    </>
                  )}
                </Routes>
              </Router>
            </ScholarshipProvider>
        </SecureAuthProvider>
      </LanguageProvider>
    </ErrorBoundary>
  );
}

export default App;