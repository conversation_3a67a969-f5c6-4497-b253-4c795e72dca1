import { useState, useEffect } from 'react';
import secureApiService from '../services/secureApiService';

interface AdminStats {
  totalScholarships: number;
  totalSubscribers: number;
  totalMessages: number;
  recentActivity: any[];
}

interface UseAdminApiReturn {
  stats: AdminStats | null;
  loading: boolean;
  error: string | null;
  refreshStats: () => Promise<void>;
}

export const useAdminApi = (): UseAdminApiReturn => {
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  const fetchStats = async () => {
    try {
      setLoading(true);
      setError(null);

      // Fetch admin statistics
      const response = await secureApiService.get('/api/admin/stats') as { data: AdminStats };
      setStats(response.data);
    } catch (err: any) {
      console.error('Error fetching admin stats:', err);
      setError(err.message || 'Failed to fetch admin statistics');
      
      // Set default stats if API fails
      setStats({
        totalScholarships: 0,
        totalSubscribers: 0,
        totalMessages: 0,
        recentActivity: []
      });
    } finally {
      setLoading(false);
    }
  };

  const refreshStats = async () => {
    await fetchStats();
  };

  useEffect(() => {
    fetchStats();
  }, []);

  return {
    stats,
    loading,
    error,
    refreshStats
  };
};
