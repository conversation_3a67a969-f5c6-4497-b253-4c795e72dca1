/**
 * Utility functions for clearing localStorage and sessionStorage
 */

export const clearAllLocalStorage = (): boolean => {
  try {
    localStorage.clear();
    console.log('All localStorage data cleared');
    return true;
  } catch (error) {
    console.error('Error clearing localStorage:', error);
    return false;
  }
};

export const clearSelectedLocalStorage = (keys: string[]): boolean => {
  try {
    keys.forEach(key => {
      localStorage.removeItem(key);
    });
    console.log('Selected localStorage items cleared:', keys);
    return true;
  } catch (error) {
    console.error('Error clearing selected localStorage items:', error);
    return false;
  }
};

export const clearAllSessionStorage = (): void => {
  try {
    sessionStorage.clear();
    console.log('All sessionStorage data cleared');
  } catch (error) {
    console.error('Error clearing sessionStorage:', error);
  }
};

export const clearSelectedSessionStorage = (keys: string[]): void => {
  try {
    keys.forEach(key => {
      sessionStorage.removeItem(key);
    });
    console.log('Selected sessionStorage items cleared:', keys);
  } catch (error) {
    console.error('Error clearing selected sessionStorage items:', error);
  }
};
