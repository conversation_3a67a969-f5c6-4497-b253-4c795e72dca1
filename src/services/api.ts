import axios from 'axios';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000';

// Create axios instance with default config
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  withCredentials: true, // Important for HTTP-only cookies
  headers: {
    'Content-Type': 'application/json',
  },
});

// Newsletter API
export const newsletterAPI = {
  // Get all newsletter subscribers
  getSubscribers: async () => {
    try {
      const response = await apiClient.get('/api/newsletter/subscribers');
      return response.data;
    } catch (error) {
      console.error('Error fetching newsletter subscribers:', error);
      throw error;
    }
  },

  // Subscribe to newsletter
  subscribe: async (email: string) => {
    try {
      const response = await apiClient.post('/api/newsletter/subscribe', { email });
      return response.data;
    } catch (error) {
      console.error('Error subscribing to newsletter:', error);
      throw error;
    }
  },

  // Unsubscribe from newsletter
  unsubscribe: async (email: string) => {
    try {
      const response = await apiClient.post('/api/newsletter/unsubscribe', { email });
      return response.data;
    } catch (error) {
      console.error('Error unsubscribing from newsletter:', error);
      throw error;
    }
  },

  // Delete subscriber (admin only)
  deleteSubscriber: async (id: number) => {
    try {
      const response = await apiClient.delete(`/api/newsletter/subscribers/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting newsletter subscriber:', error);
      throw error;
    }
  },

  // Export subscribers (admin only)
  exportSubscribers: async () => {
    try {
      const response = await apiClient.get('/api/newsletter/export', {
        responseType: 'blob',
      });
      return response.data;
    } catch (error) {
      console.error('Error exporting newsletter subscribers:', error);
      throw error;
    }
  },
};

// General API utilities
export const api = {
  get: (url: string) => apiClient.get(url),
  post: (url: string, data: any) => apiClient.post(url, data),
  put: (url: string, data: any) => apiClient.put(url, data),
  delete: (url: string) => apiClient.delete(url),
};

export default apiClient;
